import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('should redirect to signin when not authenticated', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page).toHaveURL(/.*\/auth\/signin/);
  });

  test('should show signin form', async ({ page }) => {
    await page.goto('/auth/signin');
    
    await expect(page.locator('h1, h2')).toContainText('Sign in to GhostPractice');
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should show demo accounts information', async ({ page }) => {
    await page.goto('/auth/signin');
    
    await expect(page.locator('text=Demo Accounts')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should handle invalid credentials', async ({ page }) => {
    await page.goto('/auth/signin');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Invalid email or password')).toBeVisible();
  });

  test('should login with valid credentials and redirect to dashboard', async ({ page }) => {
    await page.goto('/auth/signin');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    await expect(page.locator('h1, h2')).toContainText('Dashboard');
  });

  test('should show dashboard stats after login', async ({ page }) => {
    // Login first
    await page.goto('/auth/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Check dashboard content
    await expect(page.locator('text=Active Matters')).toBeVisible();
    await expect(page.locator('text=Billable Hours')).toBeVisible();
    await expect(page.locator('text=Outstanding Invoices')).toBeVisible();
    await expect(page.locator('text=Trust Balance')).toBeVisible();
  });

  test('should show recent activity section', async ({ page }) => {
    // Login first
    await page.goto('/auth/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Check recent activity
    await expect(page.locator('text=Recent Activity')).toBeVisible();
    await expect(page.locator('text=Latest updates from your practice')).toBeVisible();
  });

  test('should show quick actions section', async ({ page }) => {
    // Login first
    await page.goto('/auth/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Check quick actions
    await expect(page.locator('text=Quick Actions')).toBeVisible();
    await expect(page.locator('text=Create New Matter')).toBeVisible();
    await expect(page.locator('text=Start Timer')).toBeVisible();
    await expect(page.locator('text=Generate Invoice')).toBeVisible();
    await expect(page.locator('text=Upload Document')).toBeVisible();
  });
});