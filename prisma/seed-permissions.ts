import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding permissions and roles...');

  // Create permissions
  const permissionCategories = [
    {
      name: 'User Management',
      permissions: [
        { code: 'user:view', name: 'View Users' },
        { code: 'user:create', name: 'Create Users' },
        { code: 'user:edit', name: 'Edit Users' },
        { code: 'user:delete', name: 'Delete Users' },
      ],
    },
    {
      name: 'Matter Management',
      permissions: [
        { code: 'matter:view', name: 'View Matters' },
        { code: 'matter:create', name: 'Create Matters' },
        { code: 'matter:edit', name: 'Edit Matters' },
        { code: 'matter:delete', name: 'Delete Matters' },
      ],
    },
    {
      name: 'Billing',
      permissions: [
        { code: 'billing:view', name: 'View Billing' },
        { code: 'billing:create', name: 'Create Invoices' },
        { code: 'billing:edit', name: 'Edit Invoices' },
        { code: 'billing:delete', name: 'Delete Invoices' },
      ],
    },
    {
      name: 'Trust Accounting',
      permissions: [
        { code: 'trust:view', name: 'View Trust Accounts' },
        { code: 'trust:create', name: 'Create Trust Transactions' },
        { code: 'trust:edit', name: 'Edit Trust Transactions' },
        { code: 'trust:delete', name: 'Delete Trust Transactions' },
      ],
    },
    {
      name: 'Document Management',
      permissions: [
        { code: 'document:view', name: 'View Documents' },
        { code: 'document:create', name: 'Create Documents' },
        { code: 'document:edit', name: 'Edit Documents' },
        { code: 'document:delete', name: 'Delete Documents' },
      ],
    },
    {
      name: 'System Administration',
      permissions: [
        { code: 'system:view', name: 'View System Settings' },
        { code: 'system:edit', name: 'Edit System Settings' },
        { code: 'role:view', name: 'View Roles' },
        { code: 'role:create', name: 'Create Roles' },
        { code: 'role:edit', name: 'Edit Roles' },
        { code: 'role:delete', name: 'Delete Roles' },
      ],
    },
  ];

  // Create permissions
  for (const category of permissionCategories) {
    for (const permission of category.permissions) {
      await prisma.permission.upsert({
        where: { code: permission.code },
        update: {},
        create: {
          code: permission.code,
          name: permission.name,
          category: category.name,
        },
      });
    }
  }

  console.log('Created permissions');

  // Create roles
  const adminRole = await prisma.role.upsert({
    where: { name: 'Administrator' },
    update: {},
    create: {
      name: 'Administrator',
      description: 'Full access to all system features',
      isSystem: true,
    },
  });

  const attorneyRole = await prisma.role.upsert({
    where: { name: 'Attorney' },
    update: {},
    create: {
      name: 'Attorney',
      description: 'Access to matters, billing, and documents',
      isSystem: true,
    },
  });

  const secretaryRole = await prisma.role.upsert({
    where: { name: 'Secretary' },
    update: {},
    create: {
      name: 'Secretary',
      description: 'Limited access to matters and documents',
      isSystem: true,
    },
  });

  const bookkeeperRole = await prisma.role.upsert({
    where: { name: 'Bookkeeper' },
    update: {},
    create: {
      name: 'Bookkeeper',
      description: 'Access to billing and trust accounting',
      isSystem: true,
    },
  });

  console.log('Created roles');

  // Assign permissions to roles
  const allPermissions = await prisma.permission.findMany();
  
  // Admin gets all permissions
  for (const permission of allPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Attorney permissions
  const attorneyPermissions = allPermissions.filter(p => 
    p.code.startsWith('matter:') || 
    p.code.startsWith('document:') || 
    p.code.startsWith('billing:view') || 
    p.code.startsWith('billing:create') ||
    p.code === 'user:view'
  );

  for (const permission of attorneyPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: attorneyRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: attorneyRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Secretary permissions
  const secretaryPermissions = allPermissions.filter(p => 
    p.code === 'matter:view' || 
    p.code === 'document:view' || 
    p.code === 'document:create' ||
    p.code === 'user:view'
  );

  for (const permission of secretaryPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: secretaryRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: secretaryRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Bookkeeper permissions
  const bookkeeperPermissions = allPermissions.filter(p => 
    p.code.startsWith('billing:') || 
    p.code.startsWith('trust:')
  );

  for (const permission of bookkeeperPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: bookkeeperRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: bookkeeperRole.id,
        permissionId: permission.id,
      },
    });
  }

  console.log('Assigned permissions to roles');

  // Update existing users with roles
  const admin = await prisma.user.findFirst({
    where: { role: 'ADMIN' },
  });

  if (admin) {
    await prisma.user.update({
      where: { id: admin.id },
      data: {
        customRole: {
          connect: { id: adminRole.id },
        },
      },
    });
  }

  const attorney = await prisma.user.findFirst({
    where: { role: 'ATTORNEY' },
  });

  if (attorney) {
    await prisma.user.update({
      where: { id: attorney.id },
      data: {
        customRole: {
          connect: { id: attorneyRole.id },
        },
      },
    });
  }

  const secretary = await prisma.user.findFirst({
    where: { role: 'SECRETARY' },
  });

  if (secretary) {
    await prisma.user.update({
      where: { id: secretary.id },
      data: {
        customRole: {
          connect: { id: secretaryRole.id },
        },
      },
    });
  }

  const bookkeeper = await prisma.user.findFirst({
    where: { role: 'BOOKKEEPER' },
  });

  if (bookkeeper) {
    await prisma.user.update({
      where: { id: bookkeeper.id },
      data: {
        customRole: {
          connect: { id: bookkeeperRole.id },
        },
      },
    });
  }

  console.log('Updated users with roles');
  console.log('✅ Seeding completed');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
