// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [pg_trgm]
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Core Application Models
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(ATTORNEY)
  roleId        String?
  isActive      Boolean   @default(true)
  mfaEnabled    Boolean   @default(false)
  mfaSecret     String?
  lastLoginAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts         Account[]
  sessions         Session[]
  matters          Matter[]
  timeEntries      TimeEntry[]
  tasks            Task[]
  documents        Document[]
  auditLogs        AuditLog[]
  invoices         Invoice[]
  receipts         Receipt[]
  payments         Payment[]
  notifications    Notification[]
  createdMatters   Matter[]       @relation("CreatedBy")
  assignedTasks    Task[]         @relation("AssignedTo")
  billableRates    BillableRate[]
  trustTransfers   TrustTransfer[]
  documentVersions DocumentVersion[]
  customRole       Role?          @relation(fields: [roleId], references: [id])
  closedPeriods    FinancialPeriod[]

  @@map("users")
}

model BillableRate {
  id        String   @id @default(cuid())
  userId    String
  rate      Decimal  @db.Decimal(10, 2)
  currency  String   @default("ZAR")
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("billable_rates")
}

model Process {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  matters     Matter[]
  stages      ProcessStage[]

  @@map("processes")
}

model ProcessStage {
  id          String   @id @default(cuid())
  processId   String
  name        String
  description String?  @db.Text
  order       Int
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  process     Process  @relation(fields: [processId], references: [id], onDelete: Cascade)
  matters     Matter[] @relation("CurrentStage")

  @@unique([processId, order])
  @@map("process_stages")
}

model Matter {
  id          String      @id @default(cuid())
  reference   String      @unique
  title       String
  description String?     @db.Text
  status      MatterStatus @default(ACTIVE)
  processId   String?
  stageId     String?
  clientId    String?
  clientName  String
  clientEmail String?
  clientPhone String?
  openedDate  DateTime    @default(now())
  closedDate  DateTime?
  userId      String
  createdById String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Custom fields as JSON
  customFields Json?

  // Relations
  user         User            @relation(fields: [userId], references: [id])
  createdBy    User            @relation("CreatedBy", fields: [createdById], references: [id])
  process      Process?        @relation(fields: [processId], references: [id])
  currentStage ProcessStage?   @relation("CurrentStage", fields: [stageId], references: [id])
  client       Client?         @relation("ClientMatters", fields: [clientId], references: [id])
  parties      Party[]
  documents    Document[]
  timeEntries  TimeEntry[]
  tasks        Task[]
  invoices     Invoice[]
  trustAccount TrustAccount[]
  disbursements Disbursement[]

  @@map("matters")
}

model ClientGroup {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  clients     Client[]

  @@map("client_groups")
}

model Client {
  id          String   @id @default(cuid())
  name        String
  email       String?
  phone       String?
  address     String?  @db.Text
  groupId     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  group       ClientGroup? @relation(fields: [groupId], references: [id])
  parties     Party[]
  matters     Matter[]     @relation("ClientMatters")

  @@map("clients")
}

model Party {
  id       String    @id @default(cuid())
  matterId String
  clientId String?
  name     String
  type     PartyType
  email    String?
  phone    String?
  address  String?   @db.Text

  matter Matter @relation(fields: [matterId], references: [id], onDelete: Cascade)
  client Client? @relation(fields: [clientId], references: [id])

  @@map("parties")
}

model Document {
  id          String   @id @default(cuid())
  matterId    String
  userId      String
  filename    String
  originalName String
  mimeType    String
  size        Int
  path        String
  tags        String[]
  description String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  matter   Matter            @relation(fields: [matterId], references: [id], onDelete: Cascade)
  user     User              @relation(fields: [userId], references: [id])
  versions DocumentVersion[]

  @@map("documents")
}

model DocumentVersion {
  id         String   @id @default(cuid())
  documentId String
  userId     String
  version    Int
  filename   String
  path       String
  size       Int
  createdAt  DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id])

  @@unique([documentId, version])
  @@map("document_versions")
}

model TimeEntry {
  id          String    @id @default(cuid())
  matterId    String
  userId      String
  description String    @db.Text
  hours       Decimal   @db.Decimal(5, 2)
  rate        Decimal   @db.Decimal(10, 2)
  amount      Decimal   @db.Decimal(10, 2)
  date        DateTime
  isBillable  Boolean   @default(true)
  isInvoiced  Boolean   @default(false)
  invoiceId   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  matter  Matter   @relation(fields: [matterId], references: [id], onDelete: Cascade)
  user    User     @relation(fields: [userId], references: [id])
  invoice Invoice? @relation(fields: [invoiceId], references: [id])

  @@map("time_entries")
}

model Task {
  id          String     @id @default(cuid())
  matterId    String?
  userId      String
  assignedId  String?
  title       String
  description String?    @db.Text
  status      TaskStatus @default(TODO)
  priority    Priority   @default(MEDIUM)
  dueDate     DateTime?
  completedAt DateTime?
  isRecurring Boolean    @default(false)
  recurringPattern String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  matter   Matter? @relation(fields: [matterId], references: [id], onDelete: Cascade)
  user     User    @relation(fields: [userId], references: [id])
  assigned User?   @relation("AssignedTo", fields: [assignedId], references: [id])

  @@map("tasks")
}

model Invoice {
  id           String        @id @default(cuid())
  matterId     String
  userId       String
  number       String        @unique
  status       InvoiceStatus @default(DRAFT)
  issueDate    DateTime      @default(now())
  dueDate      DateTime
  subtotal     Decimal       @db.Decimal(12, 2)
  vatAmount    Decimal       @db.Decimal(12, 2) @default(0)
  total        Decimal       @db.Decimal(12, 2)
  currency     String        @default("ZAR")
  notes        String?       @db.Text
  paidAt       DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  matter        Matter          @relation(fields: [matterId], references: [id])
  user          User            @relation(fields: [userId], references: [id])
  timeEntries   TimeEntry[]
  lineItems     InvoiceItem[]
  payments      Payment[]
  disbursements Disbursement[]

  @@map("invoices")
}

model InvoiceItem {
  id          String  @id @default(cuid())
  invoiceId   String
  description String  @db.Text
  quantity    Decimal @db.Decimal(10, 2) @default(1)
  rate        Decimal @db.Decimal(10, 2)
  amount      Decimal @db.Decimal(10, 2)

  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

model Payment {
  id            String        @id @default(cuid())
  invoiceId     String?
  userId        String
  amount        Decimal       @db.Decimal(12, 2)
  currency      String        @default("ZAR")
  method        PaymentMethod
  reference     String?
  description   String?       @db.Text
  date          DateTime      @default(now())
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  invoice Invoice? @relation(fields: [invoiceId], references: [id])
  user    User     @relation(fields: [userId], references: [id])

  @@map("payments")
}

model TrustAccount {
  id        String   @id @default(cuid())
  matterId  String
  name      String
  balance   Decimal  @db.Decimal(12, 2) @default(0)
  currency  String   @default("ZAR")
  bankId    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  matter    Matter          @relation(fields: [matterId], references: [id], onDelete: Cascade)
  bank      Bank?           @relation(fields: [bankId], references: [id])
  transfers TrustTransfer[]

  @@map("trust_accounts")
}

model Bank {
  id          String   @id @default(cuid())
  name        String
  branchCode  String?
  accountType String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  trustAccounts TrustAccount[]

  @@map("banks")
}

model BusinessCreditor {
  id          String   @id @default(cuid())
  name        String
  email       String?
  phone       String?
  address     String?  @db.Text
  accountNumber String?
  vatNumber   String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  disbursements Disbursement[]

  @@map("business_creditors")
}

model Disbursement {
  id          String   @id @default(cuid())
  matterId    String?
  creditorId  String
  description String   @db.Text
  amount      Decimal  @db.Decimal(12, 2)
  vatAmount   Decimal  @db.Decimal(12, 2) @default(0)
  date        DateTime @default(now())
  status      DisbursementStatus @default(PENDING)
  invoiceId   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  matter    Matter?          @relation(fields: [matterId], references: [id], onDelete: SetNull)
  creditor  BusinessCreditor @relation(fields: [creditorId], references: [id])
  invoice   Invoice?         @relation(fields: [invoiceId], references: [id])

  @@map("disbursements")
}

model TrustTransfer {
  id              String            @id @default(cuid())
  trustAccountId  String
  userId          String
  type            TrustTransferType
  amount          Decimal           @db.Decimal(12, 2)
  description     String            @db.Text
  reference       String?
  date            DateTime          @default(now())
  createdAt       DateTime          @default(now())

  // Relations
  trustAccount TrustAccount @relation(fields: [trustAccountId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id])

  @@map("trust_transfers")
}

model Receipt {
  id          String   @id @default(cuid())
  userId      String
  number      String   @unique
  vendor      String
  amount      Decimal  @db.Decimal(10, 2)
  currency    String   @default("ZAR")
  date        DateTime
  description String?  @db.Text
  category    String?
  filePath    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("receipts")
}

model CourtTariff {
  id          String   @id @default(cuid())
  court       String
  description String   @db.Text
  amount      Decimal  @db.Decimal(10, 2)
  currency    String   @default("ZAR")
  category    String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("court_tariffs")
}

model Notification {
  id        String             @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String             @db.Text
  isRead    Boolean            @default(false)
  data      Json?
  createdAt DateTime           @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  resource  String
  resourceId String?
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model SystemStatusLog {
  id          String       @id @default(cuid())
  component   String
  status      SystemStatus
  message     String?      @db.Text
  details     Json?
  createdAt   DateTime     @default(now())

  @@map("system_status_logs")
}

model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String   @db.Text
  description String?  @db.Text
  isSecret    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_configs")
}

model PostingCode {
  id          String   @id @default(cuid())
  code        String   @unique
  description String   @db.Text
  category    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("posting_codes")
}

model FinancialPeriod {
  id          String   @id @default(cuid())
  name        String
  startDate   DateTime
  endDate     DateTime
  isClosed    Boolean  @default(false)
  closedAt    DateTime?
  closedById  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  closedBy    User?    @relation(fields: [closedById], references: [id])

  @@map("financial_periods")
}

model Permission {
  id          String   @id @default(cuid())
  code        String   @unique
  name        String
  description String?
  category    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]

  @@map("permissions")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isSystem    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]
  users           User[]

  @@map("roles")
}

model RolePermission {
  id           String   @id @default(cuid())
  roleId       String
  permissionId String
  createdAt    DateTime @default(now())

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

// Enums
enum UserRole {
  ADMIN
  ATTORNEY
  SECRETARY
  BOOKKEEPER
  SYSTEM_ADMINISTRATOR
  FEE_EARNER
  CUSTOM
}

enum MatterStatus {
  ACTIVE
  CLOSED
  ARCHIVED
}

enum PartyType {
  CLIENT
  OPPOSING_PARTY
  WITNESS
  EXPERT
  OTHER
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  CREDIT_CARD
  CHEQUE
  OTHER
}

enum TrustTransferType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  INTEREST
}

enum NotificationType {
  TASK_DUE
  INVOICE_OVERDUE
  MATTER_UPDATE
  SYSTEM
  EMAIL
}

enum DisbursementStatus {
  PENDING
  APPROVED
  PAID
  CANCELLED
}

enum SystemStatus {
  NORMAL
  WARNING
  ERROR
  MAINTENANCE
}

enum ClientPortalAccess {
  NONE
  VIEW_ONLY
  DOWNLOAD
  UPLOAD
  FULL_ACCESS
}