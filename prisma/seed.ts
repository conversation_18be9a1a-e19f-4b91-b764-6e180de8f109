import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'System Administrator',
      password: hashedPassword,
      role: UserRole.ADMIN,
      isActive: true,
    },
  });

  // Create attorney user
  const attorney = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: UserRole.ATTORNEY,
      isActive: true,
    },
  });

  // Create secretary user
  const secretary = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON> <PERSON>',
      password: hashedPassword,
      role: UserRole.SECRETARY,
      isActive: true,
    },
  });

  // Create bookkeeper user
  const bookkeeper = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Bob Bookkeeper',
      password: hashedPassword,
      role: UserRole.BOOKKEEPER,
      isActive: true,
    },
  });

  // Create billable rates
  await prisma.billableRate.createMany({
    data: [
      {
        userId: attorney.id,
        rate: 2500.00,
        currency: 'ZAR',
        isDefault: true,
      },
      {
        userId: secretary.id,
        rate: 800.00,
        currency: 'ZAR',
        isDefault: true,
      },
    ],
    skipDuplicates: true,
  });

  // Create sample matters
  const matter1 = await prisma.matter.create({
    data: {
      reference: 'MAT-2024-001',
      title: 'Smith vs Jones - Contract Dispute',
      description: 'Commercial contract dispute regarding breach of service agreement',
      clientName: 'John Smith',
      clientEmail: '<EMAIL>',
      clientPhone: '+27 11 123 4567',
      userId: attorney.id,
      createdById: attorney.id,
      customFields: {
        caseValue: 500000,
        courtLocation: 'Johannesburg High Court',
        opposingCounsel: 'Williams & Associates'
      }
    },
  });

  const matter2 = await prisma.matter.create({
    data: {
      reference: 'MAT-2024-002',
      title: 'Estate of Mary Johnson',
      description: 'Estate administration and will execution',
      clientName: 'Estate of Mary Johnson',
      clientEmail: '<EMAIL>',
      clientPhone: '+27 21 987 6543',
      userId: attorney.id,
      createdById: attorney.id,
      customFields: {
        estateValue: 2500000,
        executorName: 'Robert Johnson',
        dateOfDeath: '2024-01-15'
      }
    },
  });

  // Create parties for matters
  await prisma.party.createMany({
    data: [
      {
        matterId: matter1.id,
        name: 'John Smith',
        type: 'CLIENT',
        email: '<EMAIL>',
        phone: '+27 11 123 4567',
        address: '123 Main Street, Johannesburg, 2001'
      },
      {
        matterId: matter1.id,
        name: 'David Jones',
        type: 'OPPOSING_PARTY',
        email: '<EMAIL>',
        phone: '+27 11 765 4321',
        address: '456 Oak Avenue, Sandton, 2146'
      },
      {
        matterId: matter2.id,
        name: 'Robert Johnson',
        type: 'CLIENT',
        email: '<EMAIL>',
        phone: '+27 21 987 6543',
        address: '789 Pine Road, Cape Town, 8001'
      }
    ],
  });

  // Create sample time entries
  await prisma.timeEntry.createMany({
    data: [
      {
        matterId: matter1.id,
        userId: attorney.id,
        description: 'Initial client consultation and case review',
        hours: 2.5,
        rate: 2500.00,
        amount: 6250.00,
        date: new Date('2024-01-10'),
      },
      {
        matterId: matter1.id,
        userId: attorney.id,
        description: 'Draft pleadings and review contract documents',
        hours: 4.0,
        rate: 2500.00,
        amount: 10000.00,
        date: new Date('2024-01-15'),
      },
      {
        matterId: matter2.id,
        userId: attorney.id,
        description: 'Estate planning consultation',
        hours: 1.5,
        rate: 2500.00,
        amount: 3750.00,
        date: new Date('2024-01-20'),
      }
    ],
  });

  // Create sample tasks
  await prisma.task.createMany({
    data: [
      {
        matterId: matter1.id,
        userId: attorney.id,
        assignedId: secretary.id,
        title: 'File court documents',
        description: 'File pleadings with Johannesburg High Court',
        status: 'TODO',
        priority: 'HIGH',
        dueDate: new Date('2024-02-01'),
      },
      {
        matterId: matter1.id,
        userId: attorney.id,
        title: 'Prepare discovery documents',
        description: 'Compile and organize all relevant documents for discovery',
        status: 'IN_PROGRESS',
        priority: 'MEDIUM',
        dueDate: new Date('2024-02-15'),
      },
      {
        matterId: matter2.id,
        userId: attorney.id,
        assignedId: secretary.id,
        title: 'Obtain death certificate copies',
        description: 'Request certified copies of death certificate from Home Affairs',
        status: 'TODO',
        priority: 'MEDIUM',
        dueDate: new Date('2024-01-30'),
      }
    ],
  });

  // Create trust accounts
  const trustAccount1 = await prisma.trustAccount.create({
    data: {
      matterId: matter1.id,
      name: 'Smith vs Jones Trust Account',
      balance: 50000.00,
    },
  });

  const trustAccount2 = await prisma.trustAccount.create({
    data: {
      matterId: matter2.id,
      name: 'Johnson Estate Trust Account',
      balance: 100000.00,
    },
  });

  // Create trust transfers
  await prisma.trustTransfer.createMany({
    data: [
      {
        trustAccountId: trustAccount1.id,
        userId: attorney.id,
        type: 'DEPOSIT',
        amount: 50000.00,
        description: 'Initial client deposit for legal fees',
        reference: 'DEP-001',
        date: new Date('2024-01-10'),
      },
      {
        trustAccountId: trustAccount2.id,
        userId: attorney.id,
        type: 'DEPOSIT',
        amount: 100000.00,
        description: 'Estate funds deposit',
        reference: 'DEP-002',
        date: new Date('2024-01-20'),
      }
    ],
  });

  // Create court tariffs
  await prisma.courtTariff.createMany({
    data: [
      {
        court: 'High Court',
        description: 'Application fee',
        amount: 500.00,
        category: 'Applications',
      },
      {
        court: 'High Court',
        description: 'Motion court fee',
        amount: 300.00,
        category: 'Motions',
      },
      {
        court: 'Magistrate Court',
        description: 'Summons fee',
        amount: 150.00,
        category: 'Summons',
      },
      {
        court: 'Magistrate Court',
        description: 'Default judgment fee',
        amount: 100.00,
        category: 'Judgments',
      }
    ],
  });

  // Create sample notifications
  await prisma.notification.createMany({
    data: [
      {
        userId: attorney.id,
        type: 'TASK_DUE',
        title: 'Task Due Soon',
        message: 'Task "File court documents" is due in 2 days',
        data: { taskId: 'task-id-placeholder', dueDate: '2024-02-01' }
      },
      {
        userId: secretary.id,
        type: 'TASK_DUE',
        title: 'New Task Assigned',
        message: 'You have been assigned a new task: "File court documents"',
        data: { taskId: 'task-id-placeholder', assignedBy: attorney.id }
      }
    ],
  });

  console.log('✅ Database seeded successfully!');
  console.log('👤 Users created:');
  console.log('   - <EMAIL> (password: admin123)');
  console.log('   - <EMAIL> (password: admin123)');
  console.log('   - <EMAIL> (password: admin123)');
  console.log('   - <EMAIL> (password: admin123)');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });