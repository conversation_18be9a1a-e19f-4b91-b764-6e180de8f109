# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# prisma
/prisma/migrations/

# uploads
/uploads
/public/uploads

# logs
logs
*.log

# runtime data
pids
*.pid
*.seed
*.pid.lock

# coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# node-waf configuration
.lock-wscript

# compiled binary addons
build/Release

# dependency directories
node_modules/
jspm_packages/

# optional npm cache directory
.npm

# optional eslint cache
.eslintcache

# microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# optional REPL history
.node_repl_history

# output of 'npm pack'
*.tgz

# yarn integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# serverless directories
.serverless/

# fusebox cache
.fusebox/

# dynamodb local files
.dynamodb/

# ternjs port file
.tern-port

# stores vscode versions used for testing vscode extensions
.vscode-test

# playwright
/test-results/
/playwright-report/
/playwright/.cache/

# docker
.dockerignore

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db