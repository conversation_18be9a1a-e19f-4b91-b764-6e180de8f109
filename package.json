{"name": "ghostpractice-clone", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "test": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.1.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.16", "@trpc/client": "^11.0.0-rc.553", "@trpc/next": "^11.0.0-rc.553", "@trpc/react-query": "^11.0.0-rc.553", "@trpc/server": "^11.0.0-rc.553", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "jspdf": "^2.5.2", "lucide-react": "^0.454.0", "next": "^15.1.0", "next-auth": "^4.24.10", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "recharts": "^2.13.3", "superjson": "^2.2.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@playwright/test": "^1.48.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "^15.1.0", "jsdom": "^25.0.1", "postcss": "^8.4.49", "prettier": "^3.3.3", "prisma": "^6.1.0", "tailwindcss": "^3.4.14", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^2.1.4"}, "engines": {"node": ">=18.0.0"}}