"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Database, 
  HardDrive, 
  RefreshCw, 
  Server, 
  Settings, 
  Clock,
  Calendar
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { formatDistanceToNow, format } from "date-fns";
import { SystemStatus } from "@prisma/client";

export default function SystemStatusPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("overview");

  const { data: systemStatus, isLoading, refetch } = api.system.getStatus.useQuery(
    undefined,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Refreshed",
      description: "System status has been refreshed.",
    });
  };

  const getStatusBadge = (status: SystemStatus) => {
    switch (status) {
      case "NORMAL":
        return (
          <Badge className="bg-green-500">
            <CheckCircle className="mr-1 h-3 w-3" />
            Normal
          </Badge>
        );
      case "WARNING":
        return (
          <Badge variant="outline" className="border-yellow-500 text-yellow-500">
            <AlertCircle className="mr-1 h-3 w-3" />
            Warning
          </Badge>
        );
      case "ERROR":
        return (
          <Badge variant="destructive">
            <AlertCircle className="mr-1 h-3 w-3" />
            Error
          </Badge>
        );
      case "MAINTENANCE":
        return (
          <Badge variant="outline" className="border-blue-500 text-blue-500">
            <Settings className="mr-1 h-3 w-3" />
            Maintenance
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <Activity className="mr-1 h-3 w-3" />
            Unknown
          </Badge>
        );
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">System Status</h2>
        <Button onClick={handleRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  System Time
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemStatus ? format(new Date(systemStatus.systemTime), "HH:mm:ss") : "--:--:--"}
                </div>
                <p className="text-xs text-muted-foreground">
                  {systemStatus ? format(new Date(systemStatus.systemTime), "dd MMM yyyy") : "Loading..."}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Database Size
                </CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemStatus ? formatBytes(Number(systemStatus.dbStats[0].db_size)) : "..."}
                </div>
                <p className="text-xs text-muted-foreground">
                  {systemStatus ? `${systemStatus.dbStats[0].active_connections} active connections` : "Loading..."}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  System Status
                </CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  {isLoading ? (
                    <p>Loading...</p>
                  ) : (
                    systemStatus && (
                      getStatusBadge(
                        systemStatus.components.length > 0
                          ? systemStatus.components[0].status as SystemStatus
                          : "NORMAL"
                      )
                    )
                  )}
                </div>
                <p className="mt-2 text-xs text-muted-foreground">
                  Last updated: {systemStatus ? formatDistanceToNow(new Date(systemStatus.systemTime), { addSuffix: true }) : "..."}
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
              <CardDescription>
                Current status of all system components
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex h-24 items-center justify-center">
                  <p className="text-sm text-muted-foreground">Loading system status...</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Component</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead>Message</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {systemStatus?.components.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="h-24 text-center">
                          No component status information available.
                        </TableCell>
                      </TableRow>
                    ) : (
                      systemStatus?.components.map((component: any) => (
                        <TableRow key={component.id}>
                          <TableCell className="font-medium">{component.component}</TableCell>
                          <TableCell>{getStatusBadge(component.status as SystemStatus)}</TableCell>
                          <TableCell>
                            {formatDistanceToNow(new Date(component.createdAt), { addSuffix: true })}
                          </TableCell>
                          <TableCell>{component.message || "No message"}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button variant="outline" onClick={() => router.push("/settings/system-status/history")}>
                View Status History
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Components</CardTitle>
              <CardDescription>
                Detailed information about system components
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Component details would go here */}
              <p className="text-sm text-muted-foreground">
                Detailed component information will be displayed here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
              <CardDescription>
                View and manage system configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex h-24 items-center justify-center">
                  <p className="text-sm text-muted-foreground">Loading configuration...</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Key</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {systemStatus?.config.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={3} className="h-24 text-center">
                          No configuration settings available.
                        </TableCell>
                      </TableRow>
                    ) : (
                      systemStatus?.config.map((config: any) => (
                        <TableRow key={config.id}>
                          <TableCell className="font-medium">{config.key}</TableCell>
                          <TableCell>{config.isSecret ? "********" : config.value}</TableCell>
                          <TableCell>{config.description || "No description"}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button variant="outline" onClick={() => router.push("/settings/system-status/config")}>
                Manage Configuration
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
