"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON><PERSON>, Save, Shield } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

export default function NewRolePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("basic");

  // Fetch all permissions
  const { data: permissions, isLoading: isLoadingPermissions } = api.permission.getAll.useQuery();

  // Create role mutation
  const createRole = api.role.create.useMutation({
    onSuccess: (data) => {
      toast({
        title: "Role created",
        description: "The role has been created successfully.",
      });
      router.push(`/settings/roles/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createRole.mutate({
      name,
      description,
      permissionIds: selectedPermissions,
    });
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionId]);
    } else {
      setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  // Group permissions by category
  const permissionsByCategory = permissions?.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, typeof permissions>);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push("/settings/roles")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">Create New Role</h2>
        </div>
        <Button type="submit" form="new-role-form">
          <Save className="mr-2 h-4 w-4" />
          Create Role
        </Button>
      </div>

      <form id="new-role-form" onSubmit={handleSubmit}>
        <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="basic">Basic Information</TabsTrigger>
            <TabsTrigger value="permissions">Permissions</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Role Information</CardTitle>
                <CardDescription>
                  Enter the basic information for the new role
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium">
                    Role Name
                  </label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter role name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="description" className="text-sm font-medium">
                    Description
                  </label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter role description"
                    rows={3}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="button" onClick={() => setActiveTab("permissions")}>
                  Next: Assign Permissions
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Role Permissions</CardTitle>
                <CardDescription>
                  Select the permissions to assign to this role
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {isLoadingPermissions ? (
                    <div className="flex h-24 items-center justify-center">
                      <p className="text-sm text-muted-foreground">Loading permissions...</p>
                    </div>
                  ) : permissionsByCategory && Object.entries(permissionsByCategory).length > 0 ? (
                    Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                      <div key={category} className="space-y-2">
                        <h4 className="font-medium">{category}</h4>
                        <Separator />
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 pt-2">
                          {categoryPermissions?.map((permission) => (
                            <div key={permission.id} className="flex items-center space-x-2">
                              <Checkbox 
                                id={permission.id} 
                                checked={selectedPermissions.includes(permission.id)}
                                onCheckedChange={(checked) => 
                                  handlePermissionChange(permission.id, checked as boolean)
                                }
                              />
                              <label 
                                htmlFor={permission.id} 
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {permission.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center space-y-2 py-8">
                      <Shield className="h-12 w-12 text-muted-foreground" />
                      <h3 className="text-lg font-medium">No Permissions Available</h3>
                      <p className="text-sm text-muted-foreground text-center max-w-md">
                        There are no permissions defined in the system. Permissions need to be created before they can be assigned to roles.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button type="button" variant="outline" onClick={() => setActiveTab("basic")}>
                  Back
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  Create Role
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  );
}
