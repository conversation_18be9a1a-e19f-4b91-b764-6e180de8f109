"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  ArrowLeft, 
  Shield, 
  Save, 
  Users, 
  Edit, 
  CheckCircle2, 
  XCircle 
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";

export default function RoleDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("permissions");
  const [isEditing, setIsEditing] = useState(false);

  // Fetch role data
  const { data: role, isLoading: isLoadingRole } = api.role.getById.useQuery(
    { id: params.id },
    {
      enabled: !!params.id,
      onSuccess: (data) => {
        setSelectedPermissions(data.rolePermissions.map(rp => rp.permission.id));
      },
    }
  );

  // Fetch all permissions
  const { data: permissions, isLoading: isLoadingPermissions } = api.permission.getAll.useQuery();

  // Update role permissions mutation
  const updateRolePermissions = api.role.updatePermissions.useMutation({
    onSuccess: () => {
      toast({
        title: "Permissions updated",
        description: "The role's permissions have been updated successfully.",
      });
      setIsEditing(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSavePermissions = () => {
    updateRolePermissions.mutate({
      id: params.id,
      permissionIds: selectedPermissions,
    });
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionId]);
    } else {
      setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  const isLoading = isLoadingRole || isLoadingPermissions;

  // Group permissions by category
  const permissionsByCategory = permissions?.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, typeof permissions>);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push("/settings/roles")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">Role Details</h2>
        </div>
        {isEditing ? (
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button onClick={handleSavePermissions} disabled={isLoading}>
              <Save className="mr-2 h-4 w-4" />
              Save Permissions
            </Button>
          </div>
        ) : (
          <Button onClick={() => setIsEditing(true)} disabled={isLoading || (role?.isSystem ?? false)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Permissions
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="flex h-24 items-center justify-center">
          <p className="text-sm text-muted-foreground">Loading role data...</p>
        </div>
      ) : role ? (
        <>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-xl font-semibold">{role.name}</h3>
                    {role.isSystem ? (
                      <Badge variant="secondary">System</Badge>
                    ) : (
                      <Badge variant="outline">Custom</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{role.description || "No description"}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{role._count.users} users</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{role.rolePermissions.length} permissions</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="permissions" value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="users">Assigned Users</TabsTrigger>
            </TabsList>

            <TabsContent value="permissions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Role Permissions</CardTitle>
                  <CardDescription>
                    {isEditing 
                      ? "Edit the permissions assigned to this role" 
                      : "View the permissions assigned to this role"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {permissionsByCategory && Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                      <div key={category} className="space-y-2">
                        <h4 className="font-medium">{category}</h4>
                        <Separator />
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 pt-2">
                          {categoryPermissions?.map((permission) => (
                            <div key={permission.id} className="flex items-center space-x-2">
                              {isEditing ? (
                                <Checkbox 
                                  id={permission.id} 
                                  checked={selectedPermissions.includes(permission.id)}
                                  onCheckedChange={(checked) => 
                                    handlePermissionChange(permission.id, checked as boolean)
                                  }
                                  disabled={role.isSystem}
                                />
                              ) : (
                                selectedPermissions.includes(permission.id) ? (
                                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-muted-foreground" />
                                )
                              )}
                              <label 
                                htmlFor={permission.id} 
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {permission.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                {isEditing && (
                  <CardFooter>
                    <Button onClick={handleSavePermissions} disabled={role.isSystem}>
                      <Save className="mr-2 h-4 w-4" />
                      Save Permissions
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>

            <TabsContent value="users" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Assigned Users</CardTitle>
                  <CardDescription>
                    Users that have been assigned this role
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {/* This would be populated with actual user data */}
                    <p className="text-sm text-muted-foreground">
                      No users have been assigned this role yet.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <div className="flex h-24 items-center justify-center">
          <p className="text-sm text-muted-foreground">Role not found</p>
        </div>
      )}
    </div>
  );
}
