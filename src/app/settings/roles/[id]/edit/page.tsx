"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLef<PERSON>, Save } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";

export default function EditRolePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");

  // Fetch role data
  const { data: role, isLoading } = api.role.getById.useQuery(
    { id: params.id },
    {
      enabled: !!params.id,
      onSuccess: (data) => {
        setName(data.name);
        setDescription(data.description || "");
      },
    }
  );

  // Update role mutation
  const updateRole = api.role.update.useMutation({
    onSuccess: () => {
      toast({
        title: "Role updated",
        description: "The role has been updated successfully.",
      });
      router.push(`/settings/roles/${params.id}`);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateRole.mutate({
      id: params.id,
      name,
      description,
    });
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex h-24 items-center justify-center">
          <p className="text-sm text-muted-foreground">Loading role data...</p>
        </div>
      </div>
    );
  }

  if (role?.isSystem) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push("/settings/roles")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">Edit Role</h2>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>System Role</CardTitle>
            <CardDescription>
              System roles cannot be edited
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <h3 className="text-xl font-semibold">{role.name}</h3>
              <Badge variant="secondary">System</Badge>
            </div>
            <p className="mt-2 text-sm text-muted-foreground">
              This is a system role and cannot be modified. System roles are predefined and essential for the proper functioning of the application.
            </p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => router.push(`/settings/roles/${params.id}`)}>
              Back to Role Details
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push(`/settings/roles/${params.id}`)}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">Edit Role</h2>
        </div>
        <Button type="submit" form="edit-role-form">
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Role Information</CardTitle>
          <CardDescription>
            Edit the role's basic information
          </CardDescription>
        </CardHeader>
        <form id="edit-role-form" onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Role Name
              </label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter role name"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter role description"
                rows={3}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/settings/roles/${params.id}`)}
            >
              Cancel
            </Button>
            <Button type="submit">
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
