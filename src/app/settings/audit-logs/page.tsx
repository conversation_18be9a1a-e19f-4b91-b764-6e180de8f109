"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  FileText, 
  RefreshCw, 
  Download, 
  Search, 
  Filter, 
  Calendar,
  User,
  Activity,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { format, subDays } from "date-fns";

export default function AuditLogsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [dateRange, setDateRange] = useState("7");
  const [userFilter, setUserFilter] = useState("all");
  const [actionFilter, setActionFilter] = useState("all");

  const handleRefresh = () => {
    toast({
      title: "Refreshed",
      description: "Audit logs have been refreshed.",
    });
  };

  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Audit logs export has been initiated.",
    });
  };

  // Mock audit log data
  const auditLogs = [
    {
      id: "1",
      userId: "user1",
      userName: "Jane Doe",
      action: "LOGIN",
      resource: "AUTH",
      resourceId: null,
      details: { ip: "***********", userAgent: "Chrome/90.0" },
      createdAt: new Date(2023, 4, 22, 9, 30),
    },
    {
      id: "2",
      userId: "user1",
      userName: "Jane Doe",
      action: "CREATE",
      resource: "MATTER",
      resourceId: "matter1",
      details: { matterId: "matter1", title: "Smith vs Jones" },
      createdAt: new Date(2023, 4, 22, 10, 15),
    },
    {
      id: "3",
      userId: "user2",
      userName: "John Smith",
      action: "UPDATE",
      resource: "INVOICE",
      resourceId: "invoice1",
      details: { invoiceId: "invoice1", status: "PAID" },
      createdAt: new Date(2023, 4, 22, 11, 45),
    },
    {
      id: "4",
      userId: "user3",
      userName: "Robert Johnson",
      action: "DELETE",
      resource: "DOCUMENT",
      resourceId: "doc1",
      details: { documentId: "doc1", filename: "contract.pdf" },
      createdAt: new Date(2023, 4, 22, 14, 20),
    },
    {
      id: "5",
      userId: "user1",
      userName: "Jane Doe",
      action: "VIEW",
      resource: "TRUST_ACCOUNT",
      resourceId: "trust1",
      details: { trustAccountId: "trust1", name: "Smith Trust" },
      createdAt: new Date(2023, 4, 22, 16, 10),
    },
  ];

  // Filter logs based on search, date range, user, and action
  const filteredLogs = auditLogs.filter((log) => {
    // Search filter
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch =
      searchQuery === "" ||
      log.userName.toLowerCase().includes(searchLower) ||
      log.action.toLowerCase().includes(searchLower) ||
      log.resource.toLowerCase().includes(searchLower) ||
      (log.resourceId && log.resourceId.toLowerCase().includes(searchLower));

    // Date filter
    const dateLimit = subDays(new Date(), parseInt(dateRange));
    const matchesDate = log.createdAt >= dateLimit;

    // User filter
    const matchesUser = userFilter === "all" || log.userId === userFilter;

    // Action filter
    const matchesAction = actionFilter === "all" || log.action === actionFilter;

    // Tab filter
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "security" && ["LOGIN", "LOGOUT", "PASSWORD_CHANGE"].includes(log.action)) ||
      (activeTab === "data" && ["CREATE", "UPDATE", "DELETE"].includes(log.action)) ||
      (activeTab === "system" && log.resource === "SYSTEM");

    return matchesSearch && matchesDate && matchesUser && matchesAction && matchesTab;
  });

  const getActionBadge = (action: string) => {
    switch (action) {
      case "CREATE":
        return (
          <Badge className="bg-green-500">
            <CheckCircle className="mr-1 h-3 w-3" />
            Create
          </Badge>
        );
      case "UPDATE":
        return (
          <Badge variant="outline" className="border-blue-500 text-blue-500">
            <Info className="mr-1 h-3 w-3" />
            Update
          </Badge>
        );
      case "DELETE":
        return (
          <Badge variant="destructive">
            <AlertCircle className="mr-1 h-3 w-3" />
            Delete
          </Badge>
        );
      case "LOGIN":
        return (
          <Badge className="bg-purple-500">
            <User className="mr-1 h-3 w-3" />
            Login
          </Badge>
        );
      case "VIEW":
        return (
          <Badge variant="outline">
            <Activity className="mr-1 h-3 w-3" />
            View
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <Activity className="mr-1 h-3 w-3" />
            {action}
          </Badge>
        );
    }
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Audit Logs</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Logs</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="data">Data Changes</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit Log Entries</CardTitle>
              <CardDescription>
                View and filter system audit logs
              </CardDescription>
              <div className="flex flex-col gap-4 pt-2 md:flex-row">
                <div className="flex flex-1 items-center gap-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search logs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div className="flex gap-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <Select value={dateRange} onValueChange={setDateRange}>
                      <SelectTrigger className="h-8 w-[130px]">
                        <SelectValue placeholder="Date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">Last 24 hours</SelectItem>
                        <SelectItem value="7">Last 7 days</SelectItem>
                        <SelectItem value="30">Last 30 days</SelectItem>
                        <SelectItem value="90">Last 90 days</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <Select value={userFilter} onValueChange={setUserFilter}>
                      <SelectTrigger className="h-8 w-[130px]">
                        <SelectValue placeholder="User" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="user1">Jane Doe</SelectItem>
                        <SelectItem value="user2">John Smith</SelectItem>
                        <SelectItem value="user3">Robert Johnson</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <Select value={actionFilter} onValueChange={setActionFilter}>
                      <SelectTrigger className="h-8 w-[130px]">
                        <SelectValue placeholder="Action" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Actions</SelectItem>
                        <SelectItem value="CREATE">Create</SelectItem>
                        <SelectItem value="UPDATE">Update</SelectItem>
                        <SelectItem value="DELETE">Delete</SelectItem>
                        <SelectItem value="LOGIN">Login</SelectItem>
                        <SelectItem value="VIEW">View</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        No audit logs found matching the current filters.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          {format(log.createdAt, "yyyy-MM-dd HH:mm:ss")}
                        </TableCell>
                        <TableCell>{log.userName}</TableCell>
                        <TableCell>{getActionBadge(log.action)}</TableCell>
                        <TableCell>
                          <span className="font-medium">{log.resource}</span>
                          {log.resourceId && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              {log.resourceId}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <pre className="text-xs text-muted-foreground">
                            {JSON.stringify(log.details, null, 2)}
                          </pre>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {filteredLogs.length} of {auditLogs.length} logs
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
