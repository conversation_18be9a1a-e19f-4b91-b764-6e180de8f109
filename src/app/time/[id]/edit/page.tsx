'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Save, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

export default function EditTimeEntryPage() {
  const params = useParams();
  const router = useRouter();
  const timeEntryId = params.id as string;

  // Mock data - in real app this would come from API
  const [timeEntry, setTimeEntry] = useState({
    id: timeEntryId,
    matterId: '1',
    matterReference: 'MAT-2024-001',
    matterTitle: 'Smith vs Jones - Contract Dispute',
    description: 'Initial client consultation and case review',
    hours: 2.5,
    rate: 2500,
    date: new Date('2024-01-10'),
    isBillable: true,
    isInvoiced: false
  });

  const [date, setDate] = useState<Date | undefined>(timeEntry.date);

  // Mock matters for selection
  const matters = [
    { id: '1', reference: 'MAT-2024-001', title: 'Smith vs Jones - Contract Dispute' },
    { id: '2', reference: 'MAT-2024-002', title: 'Estate of Mary Johnson' },
    { id: '3', reference: 'MAT-2024-003', title: 'Property Purchase - Green Valley' }
  ];

  const handleSave = () => {
    // In real app, this would save to API
    console.log('Saving time entry:', {
      ...timeEntry,
      date,
      amount: timeEntry.hours * timeEntry.rate
    });
    router.push('/time');
  };

  const handleCancel = () => {
    router.push('/time');
  };

  const calculateAmount = () => {
    return timeEntry.hours * timeEntry.rate;
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Edit Time Entry</h1>
        <p className="text-muted-foreground mt-1">
          Modify time entry details and billing information
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Time Entry Details</CardTitle>
          <CardDescription>
            Update the time entry information below
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Matter Selection */}
          <div className="space-y-2">
            <Label htmlFor="matter">Matter</Label>
            <Select
              value={timeEntry.matterId}
              onValueChange={(value) => {
                const selectedMatter = matters.find(m => m.id === value);
                setTimeEntry(prev => ({
                  ...prev,
                  matterId: value,
                  matterReference: selectedMatter?.reference || '',
                  matterTitle: selectedMatter?.title || ''
                }));
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a matter" />
              </SelectTrigger>
              <SelectContent>
                {matters.map((matter) => (
                  <SelectItem key={matter.id} value={matter.id}>
                    {matter.reference} - {matter.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date */}
          <div className="space-y-2">
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe the work performed..."
              value={timeEntry.description}
              onChange={(e) => setTimeEntry(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Hours and Rate */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="hours">Hours</Label>
              <Input
                id="hours"
                type="number"
                step="0.25"
                min="0"
                value={timeEntry.hours}
                onChange={(e) => setTimeEntry(prev => ({ ...prev, hours: parseFloat(e.target.value) || 0 }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="rate">Rate (R/hour)</Label>
              <Input
                id="rate"
                type="number"
                step="50"
                min="0"
                value={timeEntry.rate}
                onChange={(e) => setTimeEntry(prev => ({ ...prev, rate: parseFloat(e.target.value) || 0 }))}
              />
            </div>
          </div>

          {/* Calculated Amount */}
          <div className="space-y-2">
            <Label>Total Amount</Label>
            <div className="text-2xl font-bold text-green-600">
              R{calculateAmount().toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-sm text-muted-foreground">
              {timeEntry.hours} hours × R{timeEntry.rate}/hour
            </p>
          </div>

          {/* Billable Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="billable">Billable</Label>
              <p className="text-sm text-muted-foreground">
                Include this time entry in client billing
              </p>
            </div>
            <Switch
              id="billable"
              checked={timeEntry.isBillable}
              onCheckedChange={(checked) => setTimeEntry(prev => ({ ...prev, isBillable: checked }))}
            />
          </div>

          {/* Invoiced Status (Read-only) */}
          {timeEntry.isInvoiced && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-sm font-medium text-yellow-800">
                  This time entry has been invoiced
                </span>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                Changes to invoiced time entries may affect billing records.
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button onClick={handleSave} className="flex-1">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            <Button variant="outline" onClick={handleCancel} className="flex-1">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">Entry Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Entry ID:</span>
              <p className="font-mono">{timeEntry.id}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Matter:</span>
              <p>{timeEntry.matterReference}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Status:</span>
              <p>{timeEntry.isInvoiced ? 'Invoiced' : 'Not Invoiced'}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Billable:</span>
              <p>{timeEntry.isBillable ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
