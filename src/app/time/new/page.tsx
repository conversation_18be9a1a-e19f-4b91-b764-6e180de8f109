'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Save, X, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

export default function NewTimeEntryPage() {
  const router = useRouter();

  const [timeEntry, setTimeEntry] = useState({
    matterId: '',
    description: '',
    hours: 0,
    rate: 2500,
    isBillable: true
  });

  const [date, setDate] = useState<Date | undefined>(new Date());

  // Mock matters for selection
  const matters = [
    { id: '1', reference: 'MAT-2024-001', title: 'Smith vs Jones - Contract Dispute' },
    { id: '2', reference: 'MAT-2024-002', title: 'Estate of Mary Johnson' },
    { id: '3', reference: 'MAT-2024-003', title: 'Property Purchase - Green Valley' }
  ];

  const handleSave = () => {
    // In real app, this would save to API
    console.log('Creating time entry:', {
      ...timeEntry,
      date,
      amount: timeEntry.hours * timeEntry.rate
    });
    router.push('/time');
  };

  const handleCancel = () => {
    router.push('/time');
  };

  const calculateAmount = () => {
    return timeEntry.hours * timeEntry.rate;
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">New Time Entry</h1>
        <p className="text-muted-foreground mt-1">
          Record billable time for a matter
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Entry Details
          </CardTitle>
          <CardDescription>
            Enter the details for your time entry
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Matter Selection */}
          <div className="space-y-2">
            <Label htmlFor="matter">Matter *</Label>
            <Select
              value={timeEntry.matterId}
              onValueChange={(value) => setTimeEntry(prev => ({ ...prev, matterId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a matter" />
              </SelectTrigger>
              <SelectContent>
                {matters.map((matter) => (
                  <SelectItem key={matter.id} value={matter.id}>
                    {matter.reference} - {matter.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date */}
          <div className="space-y-2">
            <Label>Date *</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Describe the work performed..."
              value={timeEntry.description}
              onChange={(e) => setTimeEntry(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Hours and Rate */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="hours">Hours *</Label>
              <Input
                id="hours"
                type="number"
                step="0.25"
                min="0"
                placeholder="0.00"
                value={timeEntry.hours || ''}
                onChange={(e) => setTimeEntry(prev => ({ ...prev, hours: parseFloat(e.target.value) || 0 }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="rate">Rate (R/hour) *</Label>
              <Input
                id="rate"
                type="number"
                step="50"
                min="0"
                value={timeEntry.rate}
                onChange={(e) => setTimeEntry(prev => ({ ...prev, rate: parseFloat(e.target.value) || 0 }))}
              />
            </div>
          </div>

          {/* Calculated Amount */}
          <div className="space-y-2">
            <Label>Total Amount</Label>
            <div className="text-2xl font-bold text-green-600">
              R{calculateAmount().toLocaleString('en-ZA', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-sm text-muted-foreground">
              {timeEntry.hours} hours × R{timeEntry.rate}/hour
            </p>
          </div>

          {/* Billable Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="billable">Billable</Label>
              <p className="text-sm text-muted-foreground">
                Include this time entry in client billing
              </p>
            </div>
            <Switch
              id="billable"
              checked={timeEntry.isBillable}
              onCheckedChange={(checked) => setTimeEntry(prev => ({ ...prev, isBillable: checked }))}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button 
              onClick={handleSave} 
              className="flex-1"
              disabled={!timeEntry.matterId || !timeEntry.description || !timeEntry.hours || !date}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Time Entry
            </Button>
            <Button variant="outline" onClick={handleCancel} className="flex-1">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Tips */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">Quick Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm space-y-2 text-muted-foreground">
            <li>• Use increments of 0.25 hours (15 minutes) for accurate billing</li>
            <li>• Be specific in your description for better client transparency</li>
            <li>• Non-billable time can be used for internal tasks and administration</li>
            <li>• Your default rate is automatically applied but can be adjusted per entry</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
