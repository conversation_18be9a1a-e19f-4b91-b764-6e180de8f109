"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { 
  Clock, 
  Plus, 
  Search, 
  Filter, 
  Play, 
  Pause, 
  Calendar, 
  MoreHorizontal, 
  FileText, 
  Briefcase 
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function TimePage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [matterFilter, setMatterFilter] = useState("all");
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [timerSeconds, setTimerSeconds] = useState(0);

  // Mock data for time entries
  const timeEntries = [
    {
      id: "1",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      description: "Client consultation",
      hours: 1.5,
      rate: 250,
      amount: 375,
      date: "2024-06-01",
      isBillable: true,
      isInvoiced: false,
    },
    {
      id: "2",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      description: "Document review",
      hours: 2.25,
      rate: 250,
      amount: 562.5,
      date: "2024-06-02",
      isBillable: true,
      isInvoiced: false,
    },
    {
      id: "3",
      matterId: "2",
      matterTitle: "Johnson Estate Planning",
      description: "Draft will",
      hours: 3,
      rate: 250,
      amount: 750,
      date: "2024-06-02",
      isBillable: true,
      isInvoiced: true,
    },
    {
      id: "4",
      matterId: "3",
      matterTitle: "ABC Corporation - Merger",
      description: "Review merger agreement",
      hours: 4.5,
      rate: 300,
      amount: 1350,
      date: "2024-06-03",
      isBillable: true,
      isInvoiced: false,
    },
  ];

  // Filter time entries based on search query and matter
  const filteredTimeEntries = timeEntries.filter((entry) => {
    const matchesSearch =
      searchQuery === "" ||
      entry.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.matterTitle.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesMatter =
      matterFilter === "all" || entry.matterId === matterFilter;

    return matchesSearch && matchesMatter;
  });

  // Format timer display
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Toggle timer
  const toggleTimer = () => {
    setIsTimerRunning(!isTimerRunning);
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Time & Billing</h2>
        <div className="flex items-center space-x-2">
          <Card className="border p-2">
            <div className="flex items-center space-x-2">
              <div className="text-xl font-mono">{formatTime(timerSeconds)}</div>
              <Button 
                variant={isTimerRunning ? "destructive" : "default"} 
                size="icon"
                onClick={toggleTimer}
              >
                {isTimerRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
            </div>
          </Card>
          <Button onClick={() => router.push("/time/new")}>
            <Plus className="mr-2 h-4 w-4" />
            New Time Entry
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search time entries..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={matterFilter} onValueChange={setMatterFilter}>
          <SelectTrigger className="w-[250px]">
            <SelectValue placeholder="Filter by Matter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Matters</SelectItem>
            <SelectItem value="1">Smith vs Jones - Contract Dispute</SelectItem>
            <SelectItem value="2">Johnson Estate Planning</SelectItem>
            <SelectItem value="3">ABC Corporation - Merger</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Calendar className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Time Entries</CardTitle>
          <CardDescription>
            Manage your billable and non-billable time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Matter</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Hours</TableHead>
                <TableHead className="text-right">Rate</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTimeEntries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>{format(new Date(entry.date), "MMM d, yyyy")}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{entry.matterTitle}</span>
                    </div>
                  </TableCell>
                  <TableCell>{entry.description}</TableCell>
                  <TableCell className="text-right">{entry.hours.toFixed(2)}</TableCell>
                  <TableCell className="text-right">R {entry.rate.toFixed(2)}</TableCell>
                  <TableCell className="text-right">R {entry.amount.toFixed(2)}</TableCell>
                  <TableCell>
                    <div className={`rounded-full px-2 py-1 text-xs inline-block ${
                      entry.isInvoiced 
                        ? "bg-green-100 text-green-800" 
                        : "bg-blue-100 text-blue-800"
                    }`}>
                      {entry.isInvoiced ? "Invoiced" : "Unbilled"}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => router.push(`/time/${entry.id}/edit`)}>
                          Edit Entry
                        </DropdownMenuItem>
                        <DropdownMenuItem>Duplicate Entry</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {!entry.isInvoiced && (
                          <DropdownMenuItem>Add to Invoice</DropdownMenuItem>
                        )}
                        <DropdownMenuItem className="text-destructive">
                          Delete Entry
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
