"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  BarChart3, 
  FileText, 
  Download, 
  Calendar, 
  Clock, 
  Receipt, 
  PiggyBank, 
  Users, 
  Briefcase, 
  FileStack, 
  CheckSquare, 
  Filter 
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function ReportsPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");

  // Mock data for report categories
  const reportCategories = [
    {
      id: "financial",
      title: "Financial Reports",
      description: "Revenue, expenses, and financial performance",
      icon: <Receipt className="h-8 w-8" />,
      reports: [
        {
          id: "revenue",
          title: "Revenue Report",
          description: "Overview of revenue by month, matter, and attorney",
          path: "/reports/financial/revenue",
        },
        {
          id: "invoices",
          title: "Invoice Analysis",
          description: "Analysis of invoices, payments, and outstanding balances",
          path: "/reports/financial/invoices",
        },
        {
          id: "trust",
          title: "Trust Account Report",
          description: "Trust account balances and transactions",
          path: "/reports/financial/trust",
        },
      ],
    },
    {
      id: "time",
      title: "Time Reports",
      description: "Billable hours, utilization, and productivity",
      icon: <Clock className="h-8 w-8" />,
      reports: [
        {
          id: "billable-hours",
          title: "Billable Hours Report",
          description: "Analysis of billable hours by attorney, matter, and period",
          path: "/reports/time/billable-hours",
        },
        {
          id: "utilization",
          title: "Utilization Report",
          description: "Attorney utilization rates and productivity metrics",
          path: "/reports/time/utilization",
        },
        {
          id: "time-entry",
          title: "Time Entry Analysis",
          description: "Detailed analysis of time entries and activities",
          path: "/reports/time/time-entry",
        },
      ],
    },
    {
      id: "matters",
      title: "Matter Reports",
      description: "Matter status, progress, and performance",
      icon: <Briefcase className="h-8 w-8" />,
      reports: [
        {
          id: "matter-status",
          title: "Matter Status Report",
          description: "Overview of matter status, progress, and key metrics",
          path: "/reports/matters/status",
        },
        {
          id: "matter-financials",
          title: "Matter Financial Report",
          description: "Financial performance by matter including revenue and profitability",
          path: "/reports/matters/financials",
        },
        {
          id: "matter-activity",
          title: "Matter Activity Report",
          description: "Recent activity and updates on matters",
          path: "/reports/matters/activity",
        },
      ],
    },
    {
      id: "documents",
      title: "Document Reports",
      description: "Document usage, storage, and management",
      icon: <FileStack className="h-8 w-8" />,
      reports: [
        {
          id: "document-usage",
          title: "Document Usage Report",
          description: "Analysis of document creation, access, and storage",
          path: "/reports/documents/usage",
        },
        {
          id: "document-types",
          title: "Document Types Report",
          description: "Breakdown of document types and formats",
          path: "/reports/documents/types",
        },
      ],
    },
    {
      id: "tasks",
      title: "Task Reports",
      description: "Task completion, deadlines, and workload",
      icon: <CheckSquare className="h-8 w-8" />,
      reports: [
        {
          id: "task-status",
          title: "Task Status Report",
          description: "Overview of task status, completion rates, and deadlines",
          path: "/reports/tasks/status",
        },
        {
          id: "workload",
          title: "Workload Report",
          description: "Analysis of workload distribution across team members",
          path: "/reports/tasks/workload",
        },
      ],
    },
    {
      id: "users",
      title: "User Reports",
      description: "User activity, performance, and productivity",
      icon: <Users className="h-8 w-8" />,
      reports: [
        {
          id: "user-activity",
          title: "User Activity Report",
          description: "Analysis of user activity and system usage",
          path: "/reports/users/activity",
        },
        {
          id: "performance",
          title: "Performance Report",
          description: "Performance metrics for attorneys and staff",
          path: "/reports/users/performance",
        },
      ],
    },
  ];

  // Filter report categories based on search query and category
  const filteredCategories = reportCategories.filter((category) => {
    if (categoryFilter !== "all" && category.id !== categoryFilter) {
      return false;
    }

    if (searchQuery === "") {
      return true;
    }

    const matchesCategory =
      category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesReport = category.reports.some(
      report =>
        report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return matchesCategory || matchesReport;
  });

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Reports & Analytics</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push("/reports/custom")}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Custom Report
          </Button>
          <Button variant="outline" onClick={() => router.push("/reports/scheduled")}>
            <Calendar className="mr-2 h-4 w-4" />
            Scheduled Reports
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Input
            type="search"
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {reportCategories.map(category => (
              <SelectItem key={category.id} value={category.id}>{category.title}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-6">
        {filteredCategories.map((category) => (
          <div key={category.id} className="space-y-4">
            <div className="flex items-center space-x-2">
              {category.icon}
              <h3 className="text-xl font-semibold">{category.title}</h3>
            </div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {category.reports.map((report) => (
                <Card 
                  key={report.id} 
                  className="cursor-pointer hover:bg-accent/50 transition-colors"
                  onClick={() => router.push(report.path)}
                >
                  <CardHeader>
                    <CardTitle>{report.title}</CardTitle>
                    <CardDescription>{report.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-end">
                      <Button variant="outline" size="sm">
                        <FileText className="mr-2 h-4 w-4" />
                        Generate
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Recent Reports</CardTitle>
          <CardDescription>
            Your recently generated reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-2 hover:bg-accent/50 rounded-md transition-colors">
              <div className="flex items-center space-x-4">
                <Receipt className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Revenue Report - June 2024</p>
                  <p className="text-sm text-muted-foreground">Generated on June 5, 2024</p>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-2 hover:bg-accent/50 rounded-md transition-colors">
              <div className="flex items-center space-x-4">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Billable Hours Report - May 2024</p>
                  <p className="text-sm text-muted-foreground">Generated on June 2, 2024</p>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-2 hover:bg-accent/50 rounded-md transition-colors">
              <div className="flex items-center space-x-4">
                <Briefcase className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Matter Status Report - Q2 2024</p>
                  <p className="text-sm text-muted-foreground">Generated on June 1, 2024</p>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
