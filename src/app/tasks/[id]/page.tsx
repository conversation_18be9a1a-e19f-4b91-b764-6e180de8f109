'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { 
  CheckSquare, 
  Clock, 
  User, 
  Calendar, 
  AlertCircle, 
  Edit, 
  MessageSquare,
  FileText,
  Settings
} from 'lucide-react';
import { format } from 'date-fns';

export default function TaskDetailPage() {
  const params = useParams();
  const router = useRouter();
  const taskId = params.id as string;

  const [newComment, setNewComment] = useState('');

  // Mock data - in real app this would come from API
  const task = {
    id: taskId,
    title: 'File court documents',
    description: 'Prepare and file all necessary court documents for the <PERSON> vs <PERSON> case. Ensure all forms are completed correctly and submitted before the deadline.',
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    dueDate: '2024-02-01',
    createdDate: '2024-01-15',
    assignedTo: {
      id: '1',
      name: 'Sarah Johnson',
      role: 'Attorney'
    },
    createdBy: {
      id: '2',
      name: 'Mike <PERSON>',
      role: 'Secretary'
    },
    matter: {
      id: '1',
      reference: 'MAT-2024-001',
      title: 'Smith vs Jones - Contract Dispute'
    },
    isRecurring: false,
    recurringPattern: null,
    completedAt: null,
    estimatedHours: 4,
    actualHours: 2.5
  };

  const comments = [
    {
      id: '1',
      content: 'Started working on the document preparation. Need to review the contract terms first.',
      createdAt: '2024-01-16T10:30:00Z',
      user: {
        name: 'Sarah Johnson',
        role: 'Attorney'
      }
    },
    {
      id: '2',
      content: 'Contract review completed. Moving on to drafting the court filing documents.',
      createdAt: '2024-01-18T14:15:00Z',
      user: {
        name: 'Sarah Johnson',
        role: 'Attorney'
      }
    }
  ];

  const relatedDocuments = [
    {
      id: '1',
      name: 'Court Filing Template.docx',
      uploadedAt: '2024-01-16',
      size: '245 KB'
    },
    {
      id: '2',
      name: 'Case Summary.pdf',
      uploadedAt: '2024-01-17',
      size: '1.2 MB'
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'destructive';
      case 'MEDIUM': return 'default';
      case 'LOW': return 'secondary';
      case 'URGENT': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'default';
      case 'IN_PROGRESS': return 'secondary';
      case 'TODO': return 'outline';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckSquare className="h-4 w-4 text-green-600" />;
      case 'IN_PROGRESS': return <Clock className="h-4 w-4 text-blue-600" />;
      case 'TODO': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const handleAddComment = () => {
    if (!newComment.trim()) return;
    // In real app, this would save to API
    console.log('Adding comment:', newComment);
    setNewComment('');
  };

  const isOverdue = new Date(task.dueDate) < new Date() && task.status !== 'COMPLETED';

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            {getStatusIcon(task.status)}
          </div>
          <div>
            <h1 className="text-3xl font-bold">{task.title}</h1>
            <p className="text-muted-foreground mt-1">
              {task.matter.reference} • {task.matter.title}
            </p>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant={getStatusColor(task.status)}>
                {task.status.replace('_', ' ')}
              </Badge>
              <Badge variant={getPriorityColor(task.priority)}>
                {task.priority}
              </Badge>
              {isOverdue && (
                <Badge variant="destructive">
                  Overdue
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => router.push(`/tasks/${taskId}/edit`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Task
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned To</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{task.assignedTo.name}</div>
            <p className="text-xs text-muted-foreground">{task.assignedTo.role}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Due Date</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-lg font-bold ${isOverdue ? 'text-red-600' : ''}`}>
              {format(new Date(task.dueDate), 'MMM d, yyyy')}
            </div>
            <p className="text-xs text-muted-foreground">
              {isOverdue ? 'Overdue' : 'Upcoming'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Spent</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{task.actualHours}h</div>
            <p className="text-xs text-muted-foreground">
              of {task.estimatedHours}h estimated
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comments</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{comments.length}</div>
            <p className="text-xs text-muted-foreground">
              Updates & notes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Task Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Created Date</label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {format(new Date(task.createdDate), 'MMM d, yyyy')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Created By</label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {task.createdBy.name} ({task.createdBy.role})
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Progress & Time</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusIcon(task.status)}
                    <span className="text-sm">{task.status.replace('_', ' ')}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Estimated Hours</label>
                  <p className="text-sm text-muted-foreground mt-1">{task.estimatedHours} hours</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Actual Hours</label>
                  <p className="text-sm text-muted-foreground mt-1">{task.actualHours} hours</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Progress</label>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(task.actualHours / task.estimatedHours) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round((task.actualHours / task.estimatedHours) * 100)}% complete
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="comments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Comments & Updates</CardTitle>
              <CardDescription>
                Track progress and communicate about this task
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add Comment */}
              <div className="space-y-2">
                <Textarea
                  placeholder="Add a comment or update..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  rows={3}
                />
                <Button onClick={handleAddComment} disabled={!newComment.trim()}>
                  Add Comment
                </Button>
              </div>

              {/* Comments List */}
              <div className="space-y-4">
                {comments.map((comment) => (
                  <div key={comment.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <span className="font-medium">{comment.user.name}</span>
                        <span className="text-sm text-muted-foreground ml-2">
                          ({comment.user.role})
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {format(new Date(comment.createdAt), 'MMM d, yyyy HH:mm')}
                      </span>
                    </div>
                    <p className="text-sm">{comment.content}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Related Documents</CardTitle>
              <CardDescription>
                Files and documents related to this task
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {relatedDocuments.map((doc) => (
                  <div key={doc.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        <p className="text-sm text-muted-foreground">
                          Uploaded {doc.uploadedAt} • {doc.size}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Download</Button>
                  </div>
                ))}
                
                {relatedDocuments.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No documents attached to this task</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
