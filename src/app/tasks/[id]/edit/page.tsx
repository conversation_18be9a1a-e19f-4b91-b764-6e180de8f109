'use client';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Save, X, CheckSquare, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

export default function EditTaskPage() {
  const params = useParams();
  const router = useRouter();
  const taskId = params.id as string;

  // Mock data - in real app this would come from API
  const [task, setTask] = useState({
    id: taskId,
    title: 'File court documents',
    description: 'Prepare and file all necessary court documents for the Smith vs Jones case. Ensure all forms are completed correctly and submitted before the deadline.',
    matterId: '1',
    assignedId: '1',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    estimatedHours: 4,
    isRecurring: false,
    recurringPattern: ''
  });

  const [dueDate, setDueDate] = useState<Date | undefined>(new Date('2024-02-01'));

  // Mock data
  const matters = [
    { id: '1', reference: 'MAT-2024-001', title: 'Smith vs Jones - Contract Dispute' },
    { id: '2', reference: 'MAT-2024-002', title: 'Estate of Mary Johnson' },
    { id: '3', reference: 'MAT-2024-003', title: 'Property Purchase - Green Valley' }
  ];

  const users = [
    { id: '1', name: 'Sarah Johnson', role: 'Attorney' },
    { id: '2', name: 'Mike Wilson', role: 'Secretary' },
    { id: '3', name: 'Lisa Chen', role: 'Paralegal' }
  ];

  const priorities = [
    { value: 'LOW', label: 'Low', color: 'text-green-600' },
    { value: 'MEDIUM', label: 'Medium', color: 'text-yellow-600' },
    { value: 'HIGH', label: 'High', color: 'text-orange-600' },
    { value: 'URGENT', label: 'Urgent', color: 'text-red-600' }
  ];

  const statuses = [
    { value: 'TODO', label: 'To Do' },
    { value: 'IN_PROGRESS', label: 'In Progress' },
    { value: 'COMPLETED', label: 'Completed' }
  ];

  const recurringPatterns = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'yearly', label: 'Yearly' }
  ];

  const handleSave = () => {
    // In real app, this would save to API
    console.log('Updating task:', {
      ...task,
      dueDate
    });
    router.push(`/tasks/${taskId}`);
  };

  const handleCancel = () => {
    router.push(`/tasks/${taskId}`);
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'URGENT': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'HIGH': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'MEDIUM': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'LOW': return <CheckSquare className="h-4 w-4 text-green-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Edit Task</h1>
        <p className="text-muted-foreground mt-1">
          Modify task details and assignment
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Task Details
          </CardTitle>
          <CardDescription>
            Update the task information below
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              placeholder="Enter task title..."
              value={task.title}
              onChange={(e) => setTask(prev => ({ ...prev, title: e.target.value }))}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe the task in detail..."
              value={task.description}
              onChange={(e) => setTask(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Matter Selection */}
          <div className="space-y-2">
            <Label htmlFor="matter">Related Matter</Label>
            <Select
              value={task.matterId}
              onValueChange={(value) => setTask(prev => ({ ...prev, matterId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a matter (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No matter selected</SelectItem>
                {matters.map((matter) => (
                  <SelectItem key={matter.id} value={matter.id}>
                    {matter.reference} - {matter.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Assignment, Priority, and Status */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="assigned">Assign To</Label>
              <Select
                value={task.assignedId}
                onValueChange={(value) => setTask(prev => ({ ...prev, assignedId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select assignee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Unassigned</SelectItem>
                  {users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={task.priority}
                onValueChange={(value) => setTask(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {priorities.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <div className="flex items-center gap-2">
                        {getPriorityIcon(priority.value)}
                        <span className={priority.color}>{priority.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={task.status}
                onValueChange={(value) => setTask(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Due Date and Estimated Hours */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Due Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dueDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, "PPP") : <span>Pick a due date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={setDueDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="estimatedHours">Estimated Hours</Label>
              <Input
                id="estimatedHours"
                type="number"
                step="0.5"
                min="0"
                value={task.estimatedHours}
                onChange={(e) => setTask(prev => ({ ...prev, estimatedHours: parseFloat(e.target.value) || 0 }))}
              />
            </div>
          </div>

          {/* Recurring Task */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="recurring">Recurring Task</Label>
                <p className="text-sm text-muted-foreground">
                  Create a recurring task that repeats automatically
                </p>
              </div>
              <Switch
                id="recurring"
                checked={task.isRecurring}
                onCheckedChange={(checked) => setTask(prev => ({ ...prev, isRecurring: checked }))}
              />
            </div>

            {task.isRecurring && (
              <div className="space-y-2">
                <Label htmlFor="pattern">Recurrence Pattern</Label>
                <Select
                  value={task.recurringPattern}
                  onValueChange={(value) => setTask(prev => ({ ...prev, recurringPattern: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select recurrence pattern" />
                  </SelectTrigger>
                  <SelectContent>
                    {recurringPatterns.map((pattern) => (
                      <SelectItem key={pattern.value} value={pattern.value}>
                        {pattern.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button 
              onClick={handleSave} 
              className="flex-1"
              disabled={!task.title}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            <Button variant="outline" onClick={handleCancel} className="flex-1">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Task Information */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">Task Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Task ID:</span>
              <p className="font-mono">{task.id}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Matter:</span>
              <p>{matters.find(m => m.id === task.matterId)?.reference || 'No matter'}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Assigned To:</span>
              <p>{users.find(u => u.id === task.assignedId)?.name || 'Unassigned'}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Current Status:</span>
              <p>{statuses.find(s => s.value === task.status)?.label}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
