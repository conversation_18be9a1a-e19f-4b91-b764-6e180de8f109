"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { 
  CheckSquare, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Calendar, 
  Clock, 
  Briefcase, 
  User, 
  AlertCircle 
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

export default function TasksPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  // Mock data for tasks
  const tasks = [
    {
      id: "1",
      title: "File court documents",
      description: "Prepare and file motion for summary judgment",
      status: "TODO",
      priority: "HIGH",
      dueDate: "2024-06-05",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      assignedTo: "Jane Doe",
      createdAt: "2024-06-01",
    },
    {
      id: "2",
      title: "Client meeting",
      description: "Discuss case strategy and next steps",
      status: "TODO",
      priority: "MEDIUM",
      dueDate: "2024-06-07",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      assignedTo: "Jane Doe",
      createdAt: "2024-06-01",
    },
    {
      id: "3",
      title: "Draft will",
      description: "Complete first draft of client's will",
      status: "IN_PROGRESS",
      priority: "MEDIUM",
      dueDate: "2024-06-10",
      matterId: "2",
      matterTitle: "Johnson Estate Planning",
      assignedTo: "John Williams",
      createdAt: "2024-06-02",
    },
    {
      id: "4",
      title: "Review merger agreement",
      description: "Detailed review of all merger terms and conditions",
      status: "IN_PROGRESS",
      priority: "HIGH",
      dueDate: "2024-06-08",
      matterId: "3",
      matterTitle: "ABC Corporation - Merger",
      assignedTo: "Sarah Johnson",
      createdAt: "2024-06-03",
    },
    {
      id: "5",
      title: "Prepare client invoice",
      description: "Generate invoice for services rendered in May",
      status: "COMPLETED",
      priority: "LOW",
      dueDate: "2024-06-01",
      matterId: "2",
      matterTitle: "Johnson Estate Planning",
      assignedTo: "Jane Doe",
      createdAt: "2024-05-30",
      completedAt: "2024-06-01",
    },
  ];

  // Filter tasks based on search query, status, and priority
  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      searchQuery === "" ||
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.matterTitle.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || task.status === statusFilter;

    const matchesPriority =
      priorityFilter === "all" || task.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Group tasks by status
  const todoTasks = filteredTasks.filter(task => task.status === "TODO");
  const inProgressTasks = filteredTasks.filter(task => task.status === "IN_PROGRESS");
  const completedTasks = filteredTasks.filter(task => task.status === "COMPLETED");

  // Get priority badge color
  const getPriorityBadge = (priority) => {
    switch (priority) {
      case "HIGH":
        return "bg-red-100 text-red-800";
      case "MEDIUM":
        return "bg-blue-100 text-blue-800";
      case "LOW":
        return "bg-green-100 text-green-800";
      case "URGENT":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Task card component
  const TaskCard = ({ task }) => {
    const isOverdue = new Date(task.dueDate) < new Date() && task.status !== "COMPLETED";
    
    return (
      <Card className="mb-3">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="font-medium">{task.title}</h3>
                <div className={`rounded-full px-2 py-1 text-xs ${getPriorityBadge(task.priority)}`}>
                  {task.priority}
                </div>
                {isOverdue && (
                  <div className="rounded-full bg-red-100 text-red-800 px-2 py-1 text-xs flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Overdue
                  </div>
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => router.push(`/tasks/${task.id}`)}>
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push(`/tasks/${task.id}/edit`)}>
                  Edit Task
                </DropdownMenuItem>
                {task.status === "TODO" && (
                  <DropdownMenuItem>Mark as In Progress</DropdownMenuItem>
                )}
                {task.status !== "COMPLETED" && (
                  <DropdownMenuItem>Mark as Completed</DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-destructive">
                  Delete Task
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="mt-4 flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Briefcase className="h-4 w-4 text-muted-foreground mr-1" />
                <span className="text-muted-foreground">{task.matterTitle}</span>
              </div>
              <div className="flex items-center">
                <User className="h-4 w-4 text-muted-foreground mr-1" />
                <span className="text-muted-foreground">{task.assignedTo}</span>
              </div>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-muted-foreground mr-1" />
              <span className={isOverdue ? "text-red-600 font-medium" : "text-muted-foreground"}>
                {format(new Date(task.dueDate), "MMM d, yyyy")}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Tasks</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push("/tasks/calendar")}>
            <Calendar className="mr-2 h-4 w-4" />
            Calendar View
          </Button>
          <Button onClick={() => router.push("/tasks/new")}>
            <Plus className="mr-2 h-4 w-4" />
            New Task
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search tasks..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="TODO">To Do</SelectItem>
            <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
            <SelectItem value="COMPLETED">Completed</SelectItem>
          </SelectContent>
        </Select>
        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priorities</SelectItem>
            <SelectItem value="HIGH">High</SelectItem>
            <SelectItem value="MEDIUM">Medium</SelectItem>
            <SelectItem value="LOW">Low</SelectItem>
            <SelectItem value="URGENT">Urgent</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue="kanban" className="space-y-4">
        <TabsList>
          <TabsTrigger value="kanban">Kanban Board</TabsTrigger>
          <TabsTrigger value="list">List View</TabsTrigger>
        </TabsList>
        <TabsContent value="kanban" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h3 className="font-medium mb-3 flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                To Do ({todoTasks.length})
              </h3>
              <div className="space-y-3">
                {todoTasks.map(task => (
                  <TaskCard key={task.id} task={task} />
                ))}
                {todoTasks.length === 0 && (
                  <div className="text-center p-4 border rounded-md border-dashed text-muted-foreground">
                    No tasks to do
                  </div>
                )}
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-3 flex items-center">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                In Progress ({inProgressTasks.length})
              </h3>
              <div className="space-y-3">
                {inProgressTasks.map(task => (
                  <TaskCard key={task.id} task={task} />
                ))}
                {inProgressTasks.length === 0 && (
                  <div className="text-center p-4 border rounded-md border-dashed text-muted-foreground">
                    No tasks in progress
                  </div>
                )}
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-3 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Completed ({completedTasks.length})
              </h3>
              <div className="space-y-3">
                {completedTasks.map(task => (
                  <TaskCard key={task.id} task={task} />
                ))}
                {completedTasks.length === 0 && (
                  <div className="text-center p-4 border rounded-md border-dashed text-muted-foreground">
                    No completed tasks
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>All Tasks</CardTitle>
              <CardDescription>
                View and manage all your tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredTasks.map(task => (
                  <TaskCard key={task.id} task={task} />
                ))}
                {filteredTasks.length === 0 && (
                  <div className="text-center p-4 border rounded-md border-dashed text-muted-foreground">
                    No tasks found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
