import { redirect } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  Clock,
  FileText,
  PiggyBank,
  Briefcase,
  Receipt,
  CheckSquare,
  FileStack
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/signin");
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Welcome back</h2>
          <p className="text-muted-foreground">
            Here's an overview of your practice's performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <Clock className="mr-2 h-4 w-4" />
            Start Timer
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 bg-primary/5 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Matters
            </CardTitle>
            <div className="rounded-full bg-primary/10 p-1">
              <FileText className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold">12</div>
            <div className="mt-1 flex items-center text-xs text-muted-foreground">
              <div className="rounded-full bg-emerald-500/20 px-1 text-emerald-700 dark:text-emerald-500">+2</div>
              <span className="ml-1">from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 bg-blue-500/5 pb-2">
            <CardTitle className="text-sm font-medium">
              Billable Hours
            </CardTitle>
            <div className="rounded-full bg-blue-500/10 p-1">
              <Clock className="h-4 w-4 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold">156.5</div>
            <div className="mt-1 flex items-center text-xs text-muted-foreground">
              <span>This month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 bg-amber-500/5 pb-2">
            <CardTitle className="text-sm font-medium">
              Outstanding Invoices
            </CardTitle>
            <div className="rounded-full bg-amber-500/10 p-1">
              <FileText className="h-4 w-4 text-amber-500" />
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold">R 45,231</div>
            <div className="mt-1 flex items-center text-xs text-muted-foreground">
              <span>5 invoices pending</span>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 bg-purple-500/5 pb-2">
            <CardTitle className="text-sm font-medium">
              Trust Balance
            </CardTitle>
            <div className="rounded-full bg-purple-500/10 p-1">
              <PiggyBank className="h-4 w-4 text-purple-500" />
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold">R 125,000</div>
            <div className="mt-1 flex items-center text-xs text-muted-foreground">
              <span>Across all matters</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 border-none shadow-md">
          <CardHeader className="border-b bg-muted/30 pb-3">
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest updates from your practice
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y">
              <div className="flex items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-blue-100 text-blue-700">
                  <FileText className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">New matter created</p>
                  <p className="text-xs text-muted-foreground">Smith vs Jones - Contract Dispute</p>
                </div>
                <div className="rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium">2 hours ago</div>
              </div>

              <div className="flex items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-green-100 text-green-700">
                  <Receipt className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Invoice paid</p>
                  <p className="text-xs text-muted-foreground">INV-2024-001 - R 15,000</p>
                </div>
                <div className="rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium">4 hours ago</div>
              </div>

              <div className="flex items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-amber-100 text-amber-700">
                  <CheckSquare className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Task due soon</p>
                  <p className="text-xs text-muted-foreground">File court documents - Due tomorrow</p>
                </div>
                <div className="rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium">1 day ago</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3 border-none shadow-md">
          <CardHeader className="border-b bg-muted/30 pb-3">
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y">
              <div className="flex cursor-pointer items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <Briefcase className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">Create New Matter</div>
                  <div className="text-sm text-muted-foreground">Start a new case</div>
                </div>
              </div>

              <div className="flex cursor-pointer items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-blue-500/10 text-blue-500">
                  <Clock className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">Start Timer</div>
                  <div className="text-sm text-muted-foreground">Track billable time</div>
                </div>
              </div>

              <div className="flex cursor-pointer items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-amber-500/10 text-amber-500">
                  <Receipt className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">Generate Invoice</div>
                  <div className="text-sm text-muted-foreground">Create new invoice</div>
                </div>
              </div>

              <div className="flex cursor-pointer items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-purple-500/10 text-purple-500">
                  <FileStack className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">Upload Document</div>
                  <div className="text-sm text-muted-foreground">Add files to matters</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-2 border-none shadow-md">
          <CardHeader className="border-b bg-muted/30 pb-3">
            <CardTitle>Financial Overview</CardTitle>
            <CardDescription>
              Monthly revenue and expenses
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-lg">
              <div className="text-center">
                <BarChart className="h-16 w-16 mx-auto text-primary/40" />
                <p className="mt-2 text-sm text-muted-foreground">Financial chart will be displayed here</p>
                <Button variant="outline" size="sm" className="mt-4">
                  View Detailed Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-md">
          <CardHeader className="border-b bg-muted/30 pb-3">
            <CardTitle>Upcoming Tasks</CardTitle>
            <CardDescription>
              Tasks due in the next 7 days
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y">
              <div className="flex items-center p-4 hover:bg-muted/50 transition-colors">
                <div className="mr-4 flex h-9 w-9 items-center justify-center rounded-full bg-amber-100 text-amber-700">
                  <CheckSquare className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">File court documents</p>
                    <div className="rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800">
                      Tomorrow
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Smith vs Jones</p>
                </div>
              </div>

              <div className="flex items-center p-4 hover:bg-muted/50 transition-colors">
                <div className="mr-4 flex h-9 w-9 items-center justify-center rounded-full bg-blue-100 text-blue-700">
                  <Briefcase className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">Client meeting</p>
                    <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                      2 days
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Johnson Estate</p>
                </div>
              </div>

              <div className="flex items-center p-4 hover:bg-muted/50 transition-colors">
                <div className="mr-4 flex h-9 w-9 items-center justify-center rounded-full bg-green-100 text-green-700">
                  <FileText className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">Prepare contract</p>
                    <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      3 days
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">ABC Corporation</p>
                </div>
              </div>
            </div>
            <div className="border-t p-4">
              <Button variant="outline" size="sm" className="w-full">
                View All Tasks
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}