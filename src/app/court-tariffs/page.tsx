"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  FileText, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Download, 
  Copy 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

export default function CourtTariffsPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [courtFilter, setCourtFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  // Mock data for court tariffs
  const courtTariffs = [
    {
      id: "1",
      court: "High Court",
      description: "Appearance in court for trial (per day)",
      amount: 5000,
      currency: "ZAR",
      category: "Appearances",
      isActive: true,
    },
    {
      id: "2",
      court: "High Court",
      description: "Drafting summons",
      amount: 1500,
      currency: "ZAR",
      category: "Drafting",
      isActive: true,
    },
    {
      id: "3",
      court: "High Court",
      description: "Drafting affidavit (per page)",
      amount: 350,
      currency: "ZAR",
      category: "Drafting",
      isActive: true,
    },
    {
      id: "4",
      court: "Magistrate Court",
      description: "Appearance in court for trial (per day)",
      amount: 3000,
      currency: "ZAR",
      category: "Appearances",
      isActive: true,
    },
    {
      id: "5",
      court: "Magistrate Court",
      description: "Drafting summons",
      amount: 1000,
      currency: "ZAR",
      category: "Drafting",
      isActive: true,
    },
    {
      id: "6",
      court: "Magistrate Court",
      description: "Drafting affidavit (per page)",
      amount: 250,
      currency: "ZAR",
      category: "Drafting",
      isActive: true,
    },
    {
      id: "7",
      court: "High Court",
      description: "Consultation with client (per hour)",
      amount: 2500,
      currency: "ZAR",
      category: "Consultations",
      isActive: true,
    },
    {
      id: "8",
      court: "Magistrate Court",
      description: "Consultation with client (per hour)",
      amount: 1800,
      currency: "ZAR",
      category: "Consultations",
      isActive: true,
    },
    {
      id: "9",
      court: "High Court",
      description: "Perusal of documents (per page)",
      amount: 120,
      currency: "ZAR",
      category: "Document Review",
      isActive: true,
    },
    {
      id: "10",
      court: "Magistrate Court",
      description: "Perusal of documents (per page)",
      amount: 80,
      currency: "ZAR",
      category: "Document Review",
      isActive: true,
    },
  ];

  // Filter tariffs based on search query, court, and category
  const filteredTariffs = courtTariffs.filter((tariff) => {
    const matchesSearch =
      searchQuery === "" ||
      tariff.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tariff.court.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tariff.category.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCourt =
      courtFilter === "all" || tariff.court === courtFilter;

    const matchesCategory =
      categoryFilter === "all" || tariff.category === categoryFilter;

    return matchesSearch && matchesCourt && matchesCategory;
  });

  // Get unique courts and categories for filters
  const courts = [...new Set(courtTariffs.map(tariff => tariff.court))];
  const categories = [...new Set(courtTariffs.map(tariff => tariff.category))];

  // Group tariffs by court
  const tariffsByCourtAndCategory = courts.reduce((acc, court) => {
    acc[court] = categories.reduce((catAcc, category) => {
      catAcc[category] = filteredTariffs.filter(
        tariff => tariff.court === court && tariff.category === category
      );
      return catAcc;
    }, {});
    return acc;
  }, {});

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Court Tariffs</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push("/court-tariffs/export")}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={() => router.push("/court-tariffs/new")}>
            <Plus className="mr-2 h-4 w-4" />
            New Tariff
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search tariffs..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={courtFilter} onValueChange={setCourtFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Court" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Courts</SelectItem>
            {courts.map(court => (
              <SelectItem key={court} value={court}>{court}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>{category}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="grouped">Grouped View</TabsTrigger>
        </TabsList>
        <TabsContent value="table">
          <Card>
            <CardHeader>
              <CardTitle>Court Tariffs</CardTitle>
              <CardDescription>
                Standard tariffs for legal services in different courts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Court</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTariffs.map((tariff) => (
                    <TableRow key={tariff.id}>
                      <TableCell>{tariff.court}</TableCell>
                      <TableCell>{tariff.description}</TableCell>
                      <TableCell>{tariff.category}</TableCell>
                      <TableCell className="text-right">R {tariff.amount.toFixed(2)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => {
                              navigator.clipboard.writeText(`${tariff.description}: R ${tariff.amount.toFixed(2)}`);
                              // Show toast notification
                            }}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/court-tariffs/${tariff.id}/edit`)}>
                                Edit Tariff
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/court-tariffs/new?duplicate=${tariff.id}`)}>
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                Delete Tariff
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="grouped">
          <div className="space-y-6">
            {courts.map(court => {
              // Skip courts with no matching tariffs
              const hasTariffs = Object.values(tariffsByCourtAndCategory[court]).some(
                tariffs => tariffs.length > 0
              );
              
              if (!hasTariffs) return null;
              
              return (
                <Card key={court}>
                  <CardHeader>
                    <CardTitle>{court}</CardTitle>
                    <CardDescription>
                      Tariffs for {court}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {categories.map(category => {
                      const tariffs = tariffsByCourtAndCategory[court][category];
                      
                      if (!tariffs || tariffs.length === 0) return null;
                      
                      return (
                        <div key={category} className="space-y-2">
                          <h3 className="font-medium text-lg">{category}</h3>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Description</TableHead>
                                <TableHead className="text-right">Amount</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {tariffs.map((tariff) => (
                                <TableRow key={tariff.id}>
                                  <TableCell>{tariff.description}</TableCell>
                                  <TableCell className="text-right">R {tariff.amount.toFixed(2)}</TableCell>
                                  <TableCell className="text-right">
                                    <div className="flex justify-end space-x-1">
                                      <Button 
                                        variant="ghost" 
                                        size="icon"
                                        onClick={() => {
                                          navigator.clipboard.writeText(`${tariff.description}: R ${tariff.amount.toFixed(2)}`);
                                          // Show toast notification
                                        }}
                                      >
                                        <Copy className="h-4 w-4" />
                                      </Button>
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button variant="ghost" size="icon">
                                            <MoreHorizontal className="h-4 w-4" />
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                          <DropdownMenuItem onClick={() => router.push(`/court-tariffs/${tariff.id}/edit`)}>
                                            Edit Tariff
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => router.push(`/court-tariffs/new?duplicate=${tariff.id}`)}>
                                            Duplicate
                                          </DropdownMenuItem>
                                          <DropdownMenuSeparator />
                                          <DropdownMenuItem className="text-destructive">
                                            Delete Tariff
                                          </DropdownMenuItem>
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
