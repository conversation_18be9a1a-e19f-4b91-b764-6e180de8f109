'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, X, Briefcase, User, Phone, Mail } from 'lucide-react';

export default function NewMatterPage() {
  const router = useRouter();

  const [matter, setMatter] = useState({
    title: '',
    description: '',
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    clientAddress: '',
    assignedUserId: '',
    processId: '',
    customFields: {}
  });

  // Mock data
  const users = [
    { id: '1', name: '<PERSON>', role: 'Attorney' },
    { id: '2', name: '<PERSON>', role: 'Attorney' },
    { id: '3', name: '<PERSON>', role: 'Paralegal' }
  ];

  const processes = [
    { id: '1', name: 'Litigation', description: 'Court proceedings and disputes' },
    { id: '2', name: 'Conveyancing', description: 'Property transfers and transactions' },
    { id: '3', name: 'Estate Planning', description: 'Wills, trusts, and estate administration' },
    { id: '4', name: 'Corporate Law', description: 'Business formation and compliance' }
  ];

  const handleSave = () => {
    // In real app, this would save to API
    console.log('Creating matter:', matter);
    router.push('/matters');
  };

  const handleCancel = () => {
    router.push('/matters');
  };

  const generateReference = () => {
    const year = new Date().getFullYear();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `MAT-${year}-${random}`;
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">New Matter</h1>
        <p className="text-muted-foreground mt-1">
          Create a new legal matter and assign it to your team
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Matter Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Matter Details
              </CardTitle>
              <CardDescription>
                Basic information about the legal matter
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Matter Title *</Label>
                <Input
                  id="title"
                  placeholder="e.g., Smith vs Jones - Contract Dispute"
                  value={matter.title}
                  onChange={(e) => setMatter(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the nature of the legal matter..."
                  value={matter.description}
                  onChange={(e) => setMatter(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="assigned">Assigned Attorney</Label>
                  <Select
                    value={matter.assignedUserId}
                    onValueChange={(value) => setMatter(prev => ({ ...prev, assignedUserId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select attorney" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.filter(u => u.role === 'Attorney').map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="process">Matter Type</Label>
                  <Select
                    value={matter.processId}
                    onValueChange={(value) => setMatter(prev => ({ ...prev, processId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select matter type" />
                    </SelectTrigger>
                    <SelectContent>
                      {processes.map((process) => (
                        <SelectItem key={process.id} value={process.id}>
                          {process.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Client Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Client Information
              </CardTitle>
              <CardDescription>
                Contact details for the client
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clientName">Client Name *</Label>
                <Input
                  id="clientName"
                  placeholder="Full name or company name"
                  value={matter.clientName}
                  onChange={(e) => setMatter(prev => ({ ...prev, clientName: e.target.value }))}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="clientEmail">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="clientEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={matter.clientEmail}
                      onChange={(e) => setMatter(prev => ({ ...prev, clientEmail: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientPhone">Phone Number</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="clientPhone"
                      placeholder="+27 11 123 4567"
                      value={matter.clientPhone}
                      onChange={(e) => setMatter(prev => ({ ...prev, clientPhone: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientAddress">Address</Label>
                <Textarea
                  id="clientAddress"
                  placeholder="Client's physical address..."
                  value={matter.clientAddress}
                  onChange={(e) => setMatter(prev => ({ ...prev, clientAddress: e.target.value }))}
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Matter Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Matter Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Reference:</span>
                  <p className="font-mono text-sm">{generateReference()}</p>
                </div>
                {matter.title && (
                  <div>
                    <span className="text-sm text-muted-foreground">Title:</span>
                    <p className="font-medium">{matter.title}</p>
                  </div>
                )}
                {matter.clientName && (
                  <div>
                    <span className="text-sm text-muted-foreground">Client:</span>
                    <p>{matter.clientName}</p>
                  </div>
                )}
                {matter.assignedUserId && (
                  <div>
                    <span className="text-sm text-muted-foreground">Attorney:</span>
                    <p>{users.find(u => u.id === matter.assignedUserId)?.name}</p>
                  </div>
                )}
                {matter.processId && (
                  <div>
                    <span className="text-sm text-muted-foreground">Type:</span>
                    <p>{processes.find(p => p.id === matter.processId)?.name}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-2 text-muted-foreground">
                <li>• Use descriptive titles for easy identification</li>
                <li>• Assign matters to the appropriate attorney</li>
                <li>• Complete client contact information for billing</li>
                <li>• Select the correct matter type for proper workflow</li>
              </ul>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button 
              onClick={handleSave} 
              className="w-full"
              disabled={!matter.title || !matter.clientName}
            >
              <Save className="h-4 w-4 mr-2" />
              Create Matter
            </Button>
            <Button variant="outline" onClick={handleCancel} className="w-full">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
