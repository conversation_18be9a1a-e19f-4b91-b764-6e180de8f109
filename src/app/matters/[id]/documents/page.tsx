'use client';

import { useParams } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Upload, 
  Download, 
  Search, 
  Filter,
  MoreHorizontal,
  Eye,
  Trash2,
  Edit
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function MatterDocumentsPage() {
  const params = useParams();
  const matterId = params.id as string;
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app this would come from API
  const matter = {
    id: matterId,
    reference: `MAT-2024-${matterId.padStart(3, '0')}`,
    title: '<PERSON> vs Jones - Contract Dispute',
    clientName: '<PERSON>'
  };

  const documents = [
    {
      id: '1',
      name: 'Service Agreement.pdf',
      originalName: 'Service Agreement - Final Version.pdf',
      mimeType: 'application/pdf',
      size: '2.4 MB',
      uploadDate: '2024-01-10',
      uploadedBy: 'Sarah Johnson',
      tags: ['contract', 'agreement'],
      description: 'Original service agreement between parties'
    },
    {
      id: '2',
      name: 'Correspondence.docx',
      originalName: 'Email Correspondence - Jan 2024.docx',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      size: '156 KB',
      uploadDate: '2024-01-12',
      uploadedBy: 'John Doe',
      tags: ['correspondence', 'email'],
      description: 'Email correspondence between client and opposing party'
    },
    {
      id: '3',
      name: 'Court Filing.pdf',
      originalName: 'Summons and Particulars of Claim.pdf',
      mimeType: 'application/pdf',
      size: '1.8 MB',
      uploadDate: '2024-01-15',
      uploadedBy: 'Sarah Johnson',
      tags: ['court', 'filing', 'summons'],
      description: 'Summons and particulars of claim filed with court'
    },
    {
      id: '4',
      name: 'Evidence Photos.zip',
      originalName: 'Site Photos - Evidence.zip',
      mimeType: 'application/zip',
      size: '15.2 MB',
      uploadDate: '2024-01-18',
      uploadedBy: 'Mike Wilson',
      tags: ['evidence', 'photos'],
      description: 'Photographic evidence from site inspection'
    }
  ];

  const filteredDocuments = documents.filter(doc =>
    doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word')) return '📝';
    if (mimeType.includes('image')) return '🖼️';
    if (mimeType.includes('zip')) return '📦';
    return '📄';
  };

  const formatFileSize = (size: string) => {
    return size;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">Documents</h1>
          <p className="text-muted-foreground mt-1">
            {matter.reference} • {matter.title}
          </p>
        </div>
        <Button>
          <Upload className="h-4 w-4 mr-2" />
          Upload Document
        </Button>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Documents Grid */}
      <div className="grid grid-cols-1 gap-4">
        {filteredDocuments.map((document) => (
          <Card key={document.id} className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                  <div className="text-2xl">
                    {getFileIcon(document.mimeType)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold truncate">{document.name}</h3>
                      <span className="text-sm text-muted-foreground">
                        ({formatFileSize(document.size)})
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {document.description}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {document.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Uploaded {document.uploadDate}</span>
                      <span>by {document.uploadedBy}</span>
                      <span>Original: {document.originalName}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredDocuments.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm 
                  ? `No documents match your search for "${searchTerm}"`
                  : "No documents have been uploaded for this matter yet."
                }
              </p>
              {!searchTerm && (
                <Button>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload First Document
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{documents.length}</div>
            <p className="text-xs text-muted-foreground">
              Files uploaded
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Size</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">19.6 MB</div>
            <p className="text-xs text-muted-foreground">
              Storage used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Upload</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Jan 18</div>
            <p className="text-xs text-muted-foreground">
              Last document added
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
