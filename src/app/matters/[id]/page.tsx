'use client';

import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, FileText, Clock, DollarSign, Users, Settings } from 'lucide-react';

export default function MatterDetailPage() {
  const params = useParams();
  const matterId = params.id as string;

  // Mock data - in real app this would come from API
  const matter = {
    id: matterId,
    reference: `MAT-2024-${matterId.padStart(3, '0')}`,
    title: '<PERSON> vs Jones - Contract Dispute',
    description: 'Commercial contract dispute regarding breach of service agreement',
    status: 'ACTIVE',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    clientPhone: '+27 11 123 4567',
    openedDate: '2024-01-10',
    assignedAttorney: '<PERSON>',
    customFields: {
      caseValue: 500000,
      courtLocation: 'Johannesburg High Court',
      opposingCounsel: 'Williams & Associates'
    }
  };

  const timeEntries = [
    {
      id: '1',
      date: '2024-01-10',
      description: 'Initial client consultation and case review',
      hours: 2.5,
      rate: 2500,
      amount: 6250
    },
    {
      id: '2',
      date: '2024-01-15',
      description: 'Draft pleadings and review contract documents',
      hours: 4.0,
      rate: 2500,
      amount: 10000
    }
  ];

  const documents = [
    {
      id: '1',
      name: 'Service Agreement.pdf',
      uploadDate: '2024-01-10',
      size: '2.4 MB'
    },
    {
      id: '2',
      name: 'Correspondence.docx',
      uploadDate: '2024-01-12',
      size: '156 KB'
    }
  ];

  const tasks = [
    {
      id: '1',
      title: 'File court documents',
      status: 'TODO',
      priority: 'HIGH',
      dueDate: '2024-02-01'
    },
    {
      id: '2',
      title: 'Prepare discovery documents',
      status: 'IN_PROGRESS',
      priority: 'MEDIUM',
      dueDate: '2024-02-15'
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">{matter.title}</h1>
          <p className="text-muted-foreground mt-1">
            {matter.reference} • {matter.clientName}
          </p>
        </div>
        <div className="flex gap-2">
          <Badge variant={matter.status === 'ACTIVE' ? 'default' : 'secondary'}>
            {matter.status}
          </Badge>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Edit Matter
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">6.5h</div>
            <p className="text-xs text-muted-foreground">
              Across {timeEntries.length} entries
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R16,250</div>
            <p className="text-xs text-muted-foreground">
              Billable amount
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{documents.length}</div>
            <p className="text-xs text-muted-foreground">
              Files uploaded
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Tasks</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tasks.filter(t => t.status !== 'COMPLETED').length}</div>
            <p className="text-xs text-muted-foreground">
              Pending completion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="time">Time Entries</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Matter Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <p className="text-sm text-muted-foreground mt-1">{matter.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Opened Date</label>
                  <p className="text-sm text-muted-foreground mt-1">{matter.openedDate}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Assigned Attorney</label>
                  <p className="text-sm text-muted-foreground mt-1">{matter.assignedAttorney}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Client Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Name</label>
                  <p className="text-sm text-muted-foreground mt-1">{matter.clientName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <p className="text-sm text-muted-foreground mt-1">{matter.clientEmail}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Phone</label>
                  <p className="text-sm text-muted-foreground mt-1">{matter.clientPhone}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="time" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Time Entries</CardTitle>
              <CardDescription>
                Billable time recorded for this matter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {timeEntries.map((entry) => (
                  <div key={entry.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{entry.description}</p>
                      <p className="text-sm text-muted-foreground">{entry.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">R{entry.amount.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">{entry.hours}h @ R{entry.rate}/h</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Documents</CardTitle>
              <CardDescription>
                Files and documents related to this matter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {documents.map((doc) => (
                  <div key={doc.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        <p className="text-sm text-muted-foreground">Uploaded {doc.uploadDate}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">{doc.size}</p>
                      <Button variant="outline" size="sm">Download</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
              <CardDescription>
                Tasks and action items for this matter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {tasks.map((task) => (
                  <div key={task.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{task.title}</p>
                      <p className="text-sm text-muted-foreground">Due: {task.dueDate}</p>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant={task.priority === 'HIGH' ? 'destructive' : 'secondary'}>
                        {task.priority}
                      </Badge>
                      <Badge variant={task.status === 'TODO' ? 'outline' : 'default'}>
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
