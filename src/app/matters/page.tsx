"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Briefcase,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  FileText,
  Clock,
  CheckSquare
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

export default function MattersPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock data for matters
  const matters = [
    {
      id: "1",
      reference: "M2024-001",
      title: "<PERSON> vs Jones - Contract Dispute",
      clientName: "<PERSON> <PERSON>",
      status: "ACTIVE",
      openedDate: "2024-01-15",
      attorney: "Jane Doe",
      documentsCount: 12,
      timeEntriesCount: 24,
      tasksCount: 8,
    },
    {
      id: "2",
      reference: "M2024-002",
      title: "Johnson Estate Planning",
      clientName: "Robert Johnson",
      status: "ACTIVE",
      openedDate: "2024-02-10",
      attorney: "Jane Doe",
      documentsCount: 5,
      timeEntriesCount: 10,
      tasksCount: 3,
    },
    {
      id: "3",
      reference: "M2024-003",
      title: "ABC Corporation - Merger",
      clientName: "ABC Corporation",
      status: "ACTIVE",
      openedDate: "2024-03-05",
      attorney: "John Williams",
      documentsCount: 20,
      timeEntriesCount: 35,
      tasksCount: 12,
    },
    {
      id: "4",
      reference: "M2023-045",
      title: "Williams Divorce",
      clientName: "Sarah Williams",
      status: "CLOSED",
      openedDate: "2023-11-20",
      attorney: "Jane Doe",
      documentsCount: 8,
      timeEntriesCount: 15,
      tasksCount: 5,
    },
  ];

  // Filter matters based on search query and status
  const filteredMatters = matters.filter((matter) => {
    const matchesSearch =
      searchQuery === "" ||
      matter.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
      matter.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      matter.clientName.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || matter.status.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Matters</h2>
          <p className="text-muted-foreground">
            Manage your legal cases and client matters
          </p>
        </div>
        <Button onClick={() => router.push("/matters/new")} className="gap-1">
          <Plus className="h-4 w-4" />
          New Matter
        </Button>
      </div>

      <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search by reference, title, or client name..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center space-x-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" className="shrink-0">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid gap-4">
        {filteredMatters.length > 0 ? (
          filteredMatters.map((matter) => (
            <Card key={matter.id} className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="flex flex-col sm:flex-row sm:items-center border-b p-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                        <Briefcase className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{matter.title}</h3>
                        <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                          <span className="inline-flex items-center rounded-md bg-muted px-2 py-1 text-xs font-medium">
                            {matter.reference}
                          </span>
                          <span>•</span>
                          <span>{matter.clientName}</span>
                          <span>•</span>
                          <span>Opened: {matter.openedDate}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 mt-4 sm:mt-0">
                    <div className={`rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      matter.status === "ACTIVE"
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : matter.status === "CLOSED"
                        ? "bg-gray-100 text-gray-800 dark:bg-gray-800/50 dark:text-gray-400"
                        : "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                    }`}>
                      {matter.status}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}`)}>
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/edit`)}>
                          Edit Matter
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/documents`)}>
                          View Documents
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/time`)}>
                          View Time Entries
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/tasks`)}>
                          View Tasks
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => router.push(`/invoices/new?matterId=${matter.id}`)}>
                          Create Invoice
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-3 divide-y sm:divide-y-0 sm:divide-x">
                  <div className="flex items-center justify-center p-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-700 mr-2">
                      <FileText className="h-4 w-4" />
                    </div>
                    <span className="font-medium">{matter.documentsCount}</span>
                    <span className="ml-1 text-sm text-muted-foreground">Documents</span>
                  </div>
                  <div className="flex items-center justify-center p-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-purple-700 mr-2">
                      <Clock className="h-4 w-4" />
                    </div>
                    <span className="font-medium">{matter.timeEntriesCount}</span>
                    <span className="ml-1 text-sm text-muted-foreground">Time Entries</span>
                  </div>
                  <div className="flex items-center justify-center p-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-amber-100 text-amber-700 mr-2">
                      <CheckSquare className="h-4 w-4" />
                    </div>
                    <span className="font-medium">{matter.tasksCount}</span>
                    <span className="ml-1 text-sm text-muted-foreground">Tasks</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="border-dashed bg-muted/50">
            <CardContent className="flex flex-col items-center justify-center py-10">
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-muted">
                <Briefcase className="h-10 w-10 text-muted-foreground" />
              </div>
              <h3 className="mt-4 text-lg font-medium">No matters found</h3>
              <p className="mt-2 text-center text-sm text-muted-foreground">
                No matters match your current search criteria. Try adjusting your filters or create a new matter.
              </p>
              <Button onClick={() => router.push("/matters/new")} className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                New Matter
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
