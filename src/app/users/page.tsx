"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  User, 
  Mail, 
  Shield, 
  Clock, 
  Briefcase, 
  CheckSquare 
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";

export default function UsersPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock data for users
  const users = [
    {
      id: "1",
      name: "Jane Doe",
      email: "<EMAIL>",
      role: "ATTORNEY",
      isActive: true,
      lastLoginAt: "2024-06-04T09:30:00Z",
      createdAt: "2023-01-15T00:00:00Z",
      image: null,
      mattersCount: 15,
      timeEntriesCount: 120,
      tasksCount: 8,
    },
    {
      id: "2",
      name: "John Williams",
      email: "<EMAIL>",
      role: "ATTORNEY",
      isActive: true,
      lastLoginAt: "2024-06-03T14:45:00Z",
      createdAt: "2023-02-10T00:00:00Z",
      image: null,
      mattersCount: 12,
      timeEntriesCount: 95,
      tasksCount: 5,
    },
    {
      id: "3",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "ATTORNEY",
      isActive: true,
      lastLoginAt: "2024-06-04T11:15:00Z",
      createdAt: "2023-03-05T00:00:00Z",
      image: null,
      mattersCount: 8,
      timeEntriesCount: 75,
      tasksCount: 4,
    },
    {
      id: "4",
      name: "Michael Brown",
      email: "<EMAIL>",
      role: "SECRETARY",
      isActive: true,
      lastLoginAt: "2024-06-04T08:45:00Z",
      createdAt: "2023-04-20T00:00:00Z",
      image: null,
      mattersCount: 0,
      timeEntriesCount: 30,
      tasksCount: 12,
    },
    {
      id: "5",
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "BOOKKEEPER",
      isActive: true,
      lastLoginAt: "2024-06-03T16:30:00Z",
      createdAt: "2023-05-15T00:00:00Z",
      image: null,
      mattersCount: 0,
      timeEntriesCount: 0,
      tasksCount: 3,
    },
    {
      id: "6",
      name: "Robert Wilson",
      email: "<EMAIL>",
      role: "ADMIN",
      isActive: true,
      lastLoginAt: "2024-06-04T10:00:00Z",
      createdAt: "2023-01-10T00:00:00Z",
      image: null,
      mattersCount: 0,
      timeEntriesCount: 0,
      tasksCount: 0,
    },
    {
      id: "7",
      name: "Jennifer Taylor",
      email: "<EMAIL>",
      role: "ATTORNEY",
      isActive: false,
      lastLoginAt: "2024-05-15T14:20:00Z",
      createdAt: "2023-06-10T00:00:00Z",
      image: null,
      mattersCount: 5,
      timeEntriesCount: 45,
      tasksCount: 2,
    },
  ];

  // Filter users based on search query, role, and status
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      searchQuery === "" ||
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole =
      roleFilter === "all" || user.role === roleFilter;

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && user.isActive) ||
      (statusFilter === "inactive" && !user.isActive);

    return matchesSearch && matchesRole && matchesStatus;
  });

  // Get role badge color
  const getRoleBadge = (role) => {
    switch (role) {
      case "ADMIN":
        return "bg-purple-100 text-purple-800";
      case "ATTORNEY":
        return "bg-blue-100 text-blue-800";
      case "SECRETARY":
        return "bg-green-100 text-green-800";
      case "BOOKKEEPER":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map(part => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Users & Roles</h2>
        <Button onClick={() => router.push("/users/new")}>
          <Plus className="mr-2 h-4 w-4" />
          New User
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Users
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
            <p className="text-xs text-muted-foreground">
              {users.filter(u => u.isActive).length} active users
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Attorneys
            </CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => u.role === "ATTORNEY").length}</div>
            <p className="text-xs text-muted-foreground">
              {users.filter(u => u.role === "ATTORNEY" && u.isActive).length} active attorneys
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Support Staff
            </CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.filter(u => u.role === "SECRETARY" || u.role === "BOOKKEEPER").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Secretaries and bookkeepers
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Administrators
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => u.role === "ADMIN").length}</div>
            <p className="text-xs text-muted-foreground">
              System administrators
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search users..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="ADMIN">Admin</SelectItem>
            <SelectItem value="ATTORNEY">Attorney</SelectItem>
            <SelectItem value="SECRETARY">Secretary</SelectItem>
            <SelectItem value="BOOKKEEPER">Bookkeeper</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
          <CardDescription>
            Manage users, roles, and permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Activity</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={user.image} alt={user.name} />
                        <AvatarFallback>{getUserInitials(user.name)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className={`rounded-full px-2 py-1 text-xs inline-block ${getRoleBadge(user.role)}`}>
                      {user.role}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className={`rounded-full px-2 py-1 text-xs inline-block ${
                      user.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}>
                      {user.isActive ? "Active" : "Inactive"}
                    </div>
                  </TableCell>
                  <TableCell>
                    {user.lastLoginAt ? format(new Date(user.lastLoginAt), "MMM d, yyyy HH:mm") : "Never"}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-4">
                      <div className="flex items-center text-sm">
                        <Briefcase className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span>{user.mattersCount}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span>{user.timeEntriesCount}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckSquare className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span>{user.tasksCount}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => router.push(`/users/${user.id}`)}>
                          View Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/users/${user.id}/edit`)}>
                          Edit User
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => router.push(`/users/${user.id}/permissions`)}>
                          Manage Permissions
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/users/${user.id}/reset-password`)}>
                          Reset Password
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {user.isActive ? (
                          <DropdownMenuItem className="text-destructive">
                            Deactivate User
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem>
                            Activate User
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
