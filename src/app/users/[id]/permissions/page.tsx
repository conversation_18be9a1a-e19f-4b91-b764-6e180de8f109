"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  ArrowLeft, 
  Shield, 
  Save, 
  User, 
  Mail, 
  CheckCircle2, 
  XCircle 
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";

export default function UserPermissionsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("role");

  // Fetch user data
  const { data: user, isLoading: isLoadingUser } = api.user.getById.useQuery(
    { id: params.id },
    {
      enabled: !!params.id,
      onSuccess: (data) => {
        if (data.roleId) {
          setSelectedRole(data.roleId);
        }
      },
    }
  );

  // Fetch roles
  const { data: roles, isLoading: isLoadingRoles } = api.role.getAll.useQuery();

  // Fetch permissions
  const { data: permissions, isLoading: isLoadingPermissions } = api.permission.getAll.useQuery();

  // Fetch user's custom permissions if they have any
  const { data: userPermissions, isLoading: isLoadingUserPermissions } = api.user.getPermissions.useQuery(
    { id: params.id },
    {
      enabled: !!params.id,
      onSuccess: (data) => {
        setSelectedPermissions(data.map(p => p.id));
      },
    }
  );

  // Update user role mutation
  const updateUserRole = api.user.updateRole.useMutation({
    onSuccess: () => {
      toast({
        title: "Role updated",
        description: "The user's role has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user permissions mutation
  const updateUserPermissions = api.user.updatePermissions.useMutation({
    onSuccess: () => {
      toast({
        title: "Permissions updated",
        description: "The user's permissions have been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSaveRole = () => {
    if (selectedRole) {
      updateUserRole.mutate({
        id: params.id,
        roleId: selectedRole,
      });
    }
  };

  const handleSavePermissions = () => {
    updateUserPermissions.mutate({
      id: params.id,
      permissionIds: selectedPermissions,
    });
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionId]);
    } else {
      setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  const getUserInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const isLoading = isLoadingUser || isLoadingRoles || isLoadingPermissions || isLoadingUserPermissions;

  // Group permissions by category
  const permissionsByCategory = permissions?.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, typeof permissions>);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">User Permissions</h2>
        </div>
        {activeTab === "role" ? (
          <Button onClick={handleSaveRole} disabled={isLoading || !selectedRole}>
            <Save className="mr-2 h-4 w-4" />
            Save Role
          </Button>
        ) : (
          <Button onClick={handleSavePermissions} disabled={isLoading}>
            <Save className="mr-2 h-4 w-4" />
            Save Permissions
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="flex h-24 items-center justify-center">
          <p className="text-sm text-muted-foreground">Loading user data...</p>
        </div>
      ) : user ? (
        <>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={user.image || ""} alt={user.name || ""} />
                  <AvatarFallback>{getUserInitials(user.name || "")}</AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <h3 className="text-xl font-semibold">{user.name}</h3>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Mail className="mr-1 h-4 w-4" />
                    {user.email}
                  </div>
                  <div className="flex items-center text-sm">
                    <Shield className="mr-1 h-4 w-4 text-muted-foreground" />
                    <span>{user.role}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="role" value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="role">Role Assignment</TabsTrigger>
              <TabsTrigger value="permissions">Custom Permissions</TabsTrigger>
            </TabsList>

            <TabsContent value="role" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Assign Role</CardTitle>
                  <CardDescription>
                    Assign a role to this user to grant them a set of permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Select Role</label>
                      <Select value={selectedRole || ""} onValueChange={setSelectedRole}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles?.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedRole && roles && (
                      <div className="rounded-md border p-4">
                        <h4 className="font-medium">Role Permissions</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          This role includes the following permissions:
                        </p>
                        <div className="space-y-2">
                          {/* Display role permissions here */}
                          <p className="text-sm text-muted-foreground">
                            Role permissions will be displayed here.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleSaveRole} disabled={!selectedRole}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Role
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Custom Permissions</CardTitle>
                  <CardDescription>
                    Assign specific permissions to this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {permissionsByCategory && Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                      <div key={category} className="space-y-2">
                        <h4 className="font-medium">{category}</h4>
                        <Separator />
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 pt-2">
                          {categoryPermissions?.map((permission) => (
                            <div key={permission.id} className="flex items-center space-x-2">
                              <Checkbox 
                                id={permission.id} 
                                checked={selectedPermissions.includes(permission.id)}
                                onCheckedChange={(checked) => 
                                  handlePermissionChange(permission.id, checked as boolean)
                                }
                              />
                              <label 
                                htmlFor={permission.id} 
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {permission.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleSavePermissions}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Permissions
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <div className="flex h-24 items-center justify-center">
          <p className="text-sm text-muted-foreground">User not found</p>
        </div>
      )}
    </div>
  );
}
