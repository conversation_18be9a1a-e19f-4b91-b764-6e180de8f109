"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  FileStack,
  Upload,
  Search,
  Filter,
  MoreHorizontal,
  FileText,
  File,
  Image,
  FileType,
  Download,
  Eye,
  Briefcase
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function DocumentsPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [matterFilter, setMatterFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");

  // Mock data for documents
  const documents = [
    {
      id: "1",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      filename: "contract_draft_v1.pdf",
      originalName: "Contract Draft v1.pdf",
      mimeType: "application/pdf",
      size: 1250000,
      description: "Initial contract draft",
      createdAt: "2024-06-01T10:30:00Z",
      createdBy: "Jane Doe",
      tags: ["contract", "draft"],
    },
    {
      id: "2",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      filename: "meeting_notes_20240602.docx",
      originalName: "Meeting Notes 2024-06-02.docx",
      mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      size: 350000,
      description: "Notes from client meeting",
      createdAt: "2024-06-02T14:15:00Z",
      createdBy: "Jane Doe",
      tags: ["notes", "meeting"],
    },
    {
      id: "3",
      matterId: "2",
      matterTitle: "Johnson Estate Planning",
      filename: "will_draft.docx",
      originalName: "Will Draft.docx",
      mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      size: 420000,
      description: "Draft will for client review",
      createdAt: "2024-06-02T09:45:00Z",
      createdBy: "John Williams",
      tags: ["will", "draft"],
    },
    {
      id: "4",
      matterId: "3",
      matterTitle: "ABC Corporation - Merger",
      filename: "financial_analysis.xlsx",
      originalName: "Financial Analysis.xlsx",
      mimeType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      size: 2500000,
      description: "Financial analysis for merger",
      createdAt: "2024-06-03T11:20:00Z",
      createdBy: "Sarah Johnson",
      tags: ["financial", "analysis"],
    },
    {
      id: "5",
      matterId: "3",
      matterTitle: "ABC Corporation - Merger",
      filename: "merger_agreement.pdf",
      originalName: "Merger Agreement.pdf",
      mimeType: "application/pdf",
      size: 3100000,
      description: "Final merger agreement",
      createdAt: "2024-06-04T16:30:00Z",
      createdBy: "John Williams",
      tags: ["agreement", "final"],
    },
  ];

  // Filter documents based on search query, matter, and type
  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      searchQuery === "" ||
      doc.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.matterTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesMatter =
      matterFilter === "all" || doc.matterId === matterFilter;

    const matchesType =
      typeFilter === "all" ||
      (typeFilter === "pdf" && doc.mimeType === "application/pdf") ||
      (typeFilter === "word" && doc.mimeType.includes("wordprocessing")) ||
      (typeFilter === "excel" && doc.mimeType.includes("spreadsheet")) ||
      (typeFilter === "image" && doc.mimeType.includes("image/"));

    return matchesSearch && matchesMatter && matchesType;
  });

  // Get file icon based on mime type
  const getFileIcon = (mimeType) => {
    if (mimeType === "application/pdf") {
      return <File className="h-5 w-5 text-red-500" />;
    } else if (mimeType.includes("wordprocessing")) {
      return <FileText className="h-5 w-5 text-blue-500" />;
    } else if (mimeType.includes("spreadsheet")) {
      return <FileType className="h-5 w-5 text-green-500" />;
    } else if (mimeType.includes("image/")) {
      return <Image className="h-5 w-5 text-purple-500" />;
    } else {
      return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes < 1024) {
      return bytes + " B";
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + " KB";
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(1) + " MB";
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(1) + " GB";
    }
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Documents</h2>
        <Button onClick={() => router.push("/documents/upload")}>
          <Upload className="mr-2 h-4 w-4" />
          Upload Document
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search documents..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={matterFilter} onValueChange={setMatterFilter}>
          <SelectTrigger className="w-[250px]">
            <SelectValue placeholder="Filter by Matter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Matters</SelectItem>
            <SelectItem value="1">Smith vs Jones - Contract Dispute</SelectItem>
            <SelectItem value="2">Johnson Estate Planning</SelectItem>
            <SelectItem value="3">ABC Corporation - Merger</SelectItem>
          </SelectContent>
        </Select>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="pdf">PDF</SelectItem>
            <SelectItem value="word">Word</SelectItem>
            <SelectItem value="excel">Excel</SelectItem>
            <SelectItem value="image">Images</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Document Library</CardTitle>
          <CardDescription>
            Manage and organize your case documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Matter</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead>Uploaded</TableHead>
                <TableHead>Size</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDocuments.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell>
                    <div className="flex items-center">
                      {getFileIcon(doc.mimeType)}
                      <span className="ml-2 font-medium">{doc.originalName}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{doc.matterTitle}</span>
                    </div>
                  </TableCell>
                  <TableCell>{doc.description}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {doc.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="rounded-full bg-muted px-2 py-1 text-xs"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{format(new Date(doc.createdAt), "MMM d, yyyy")}</span>
                      <span className="text-xs text-muted-foreground">by {doc.createdBy}</span>
                    </div>
                  </TableCell>
                  <TableCell>{formatFileSize(doc.size)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Download className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => router.push(`/documents/${doc.id}`)}>
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/documents/${doc.id}/edit`)}>
                            Edit Metadata
                          </DropdownMenuItem>
                          <DropdownMenuItem>Upload New Version</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>View Version History</DropdownMenuItem>
                          <DropdownMenuItem className="text-destructive">
                            Delete Document
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
