'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Download, 
  Eye, 
  Edit, 
  Share2, 
  Trash2,
  Calendar,
  User,
  FolderOpen,
  Tag
} from 'lucide-react';
import { format } from 'date-fns';

export default function DocumentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const documentId = params.id as string;

  // Mock data - in real app this would come from API
  const document = {
    id: documentId,
    name: 'Service Agreement.pdf',
    originalName: 'Service Agreement - Final Version.pdf',
    mimeType: 'application/pdf',
    size: '2.4 MB',
    sizeBytes: 2516582,
    uploadDate: '2024-01-10T14:30:00Z',
    lastModified: '2024-01-15T09:20:00Z',
    uploadedBy: {
      id: '1',
      name: '<PERSON>',
      role: 'Attorney'
    },
    matter: {
      id: '1',
      reference: 'MAT-2024-001',
      title: 'Smith vs Jones - Contract Dispute'
    },
    tags: ['contract', 'agreement', 'final'],
    description: 'Original service agreement between parties with final amendments',
    version: '1.2',
    isPublic: false,
    downloadCount: 12,
    checksum: 'sha256:a1b2c3d4e5f6...'
  };

  const versions = [
    {
      id: '1',
      version: '1.2',
      uploadDate: '2024-01-15T09:20:00Z',
      uploadedBy: 'Sarah Johnson',
      size: '2.4 MB',
      changes: 'Final amendments and client signature'
    },
    {
      id: '2',
      version: '1.1',
      uploadDate: '2024-01-12T16:45:00Z',
      uploadedBy: 'Mike Wilson',
      size: '2.3 MB',
      changes: 'Updated terms and conditions'
    },
    {
      id: '3',
      version: '1.0',
      uploadDate: '2024-01-10T14:30:00Z',
      uploadedBy: 'Sarah Johnson',
      size: '2.2 MB',
      changes: 'Initial draft'
    }
  ];

  const accessLog = [
    {
      id: '1',
      action: 'Downloaded',
      user: 'Sarah Johnson',
      timestamp: '2024-01-20T10:15:00Z',
      ipAddress: '*************'
    },
    {
      id: '2',
      action: 'Viewed',
      user: 'Mike Wilson',
      timestamp: '2024-01-19T14:30:00Z',
      ipAddress: '*************'
    },
    {
      id: '3',
      action: 'Downloaded',
      user: 'Lisa Chen',
      timestamp: '2024-01-18T09:45:00Z',
      ipAddress: '*************'
    }
  ];

  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word')) return '📝';
    if (mimeType.includes('image')) return '🖼️';
    if (mimeType.includes('zip')) return '📦';
    return '📄';
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const handleDownload = () => {
    // In real app, this would trigger download
    console.log('Downloading document:', document.id);
  };

  const handleView = () => {
    // In real app, this would open document viewer
    console.log('Viewing document:', document.id);
  };

  const handleEdit = () => {
    router.push(`/documents/${documentId}/edit`);
  };

  const handleShare = () => {
    // In real app, this would open share dialog
    console.log('Sharing document:', document.id);
  };

  const handleDelete = () => {
    // In real app, this would show confirmation dialog
    console.log('Deleting document:', document.id);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start gap-4">
          <div className="text-4xl">
            {getFileIcon(document.mimeType)}
          </div>
          <div>
            <h1 className="text-3xl font-bold">{document.name}</h1>
            <p className="text-muted-foreground mt-1">
              {document.matter.reference} • {document.matter.title}
            </p>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary">v{document.version}</Badge>
              <Badge variant="outline">{document.size}</Badge>
              {document.isPublic ? (
                <Badge variant="default">Public</Badge>
              ) : (
                <Badge variant="secondary">Private</Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleView}>
            <Eye className="h-4 w-4 mr-2" />
            View
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">File Size</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{document.size}</div>
            <p className="text-xs text-muted-foreground">
              {formatFileSize(document.sizeBytes)}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Downloads</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{document.downloadCount}</div>
            <p className="text-xs text-muted-foreground">
              Total downloads
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Version</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">v{document.version}</div>
            <p className="text-xs text-muted-foreground">
              Current version
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Modified</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {format(new Date(document.lastModified), 'MMM d')}
            </div>
            <p className="text-xs text-muted-foreground">
              {format(new Date(document.lastModified), 'yyyy')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="versions">Versions</TabsTrigger>
          <TabsTrigger value="access">Access Log</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Document Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Original Name</label>
                  <p className="text-sm text-muted-foreground mt-1">{document.originalName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <p className="text-sm text-muted-foreground mt-1">{document.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">MIME Type</label>
                  <p className="text-sm text-muted-foreground mt-1">{document.mimeType}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Checksum</label>
                  <p className="text-sm text-muted-foreground mt-1 font-mono">{document.checksum}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upload Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Uploaded By</p>
                    <p className="text-sm text-muted-foreground">
                      {document.uploadedBy.name} ({document.uploadedBy.role})
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Upload Date</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(document.uploadDate), 'PPP p')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <FolderOpen className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Related Matter</p>
                    <p className="text-sm text-muted-foreground">
                      {document.matter.reference} - {document.matter.title}
                    </p>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium">Tags</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {document.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="versions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Version History</CardTitle>
              <CardDescription>
                Track changes and versions of this document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {versions.map((version) => (
                  <div key={version.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">Version {version.version}</span>
                        {version.version === document.version && (
                          <Badge variant="default" className="text-xs">Current</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{version.changes}</p>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(version.uploadDate), 'PPP p')} by {version.uploadedBy}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{version.size}</p>
                      <div className="flex gap-1 mt-1">
                        <Button variant="outline" size="sm">View</Button>
                        <Button variant="outline" size="sm">Download</Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="access" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Access Log</CardTitle>
              <CardDescription>
                Track who has accessed this document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accessLog.map((entry) => (
                  <div key={entry.id} className="flex justify-between items-center p-4 border rounded-lg">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{entry.user}</span>
                        <Badge variant="outline" className="text-xs">
                          {entry.action}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(entry.timestamp), 'PPP p')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-muted-foreground font-mono">
                        {entry.ipAddress}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
