'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Receipt, 
  Download, 
  Send, 
  Edit, 
  Eye,
  Calendar,
  DollarSign,
  Clock,
  User,
  FileText,
  Printer
} from 'lucide-react';
import { format } from 'date-fns';

export default function InvoiceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const invoiceId = params.id as string;

  // Mock data - in real app this would come from API
  const invoice = {
    id: invoiceId,
    number: `INV-2024-${invoiceId.padStart(4, '0')}`,
    status: 'SENT',
    issueDate: '2024-01-20',
    dueDate: '2024-02-19',
    paidDate: null,
    matter: {
      id: '1',
      reference: 'MAT-2024-001',
      title: '<PERSON> vs <PERSON> - <PERSON>tract Dispute'
    },
    client: {
      name: '<PERSON>',
      email: '<EMAIL>',
      address: '123 Main Street\nJohannesburg, 2001\nSouth Africa',
      phone: '+27 11 123 4567'
    },
    firm: {
      name: 'GhostPractice Legal',
      address: '456 Legal Avenue\nSandton, 2196\nSouth Africa',
      phone: '+27 11 555 0123',
      email: '<EMAIL>',
      vatNumber: 'VAT123456789'
    },
    lineItems: [
      {
        id: '1',
        description: 'Initial client consultation and case review',
        date: '2024-01-10',
        hours: 2.5,
        rate: 2500,
        amount: 6250
      },
      {
        id: '2',
        description: 'Draft pleadings and review contract documents',
        date: '2024-01-15',
        hours: 4.0,
        rate: 2500,
        amount: 10000
      },
      {
        id: '3',
        description: 'Court filing and document preparation',
        date: '2024-01-18',
        hours: 3.5,
        rate: 2500,
        amount: 8750
      }
    ],
    subtotal: 25000,
    vatRate: 0.15,
    vatAmount: 3750,
    total: 28750,
    currency: 'ZAR',
    notes: 'Payment terms: 30 days from invoice date. Late payments may incur interest charges.',
    createdBy: {
      name: 'Sarah Johnson',
      role: 'Attorney'
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'secondary';
      case 'SENT': return 'default';
      case 'PAID': return 'default';
      case 'OVERDUE': return 'destructive';
      case 'CANCELLED': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DRAFT': return <Edit className="h-4 w-4" />;
      case 'SENT': return <Send className="h-4 w-4" />;
      case 'PAID': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'OVERDUE': return <Clock className="h-4 w-4 text-red-600" />;
      case 'CANCELLED': return <FileText className="h-4 w-4" />;
      default: return <Receipt className="h-4 w-4" />;
    }
  };

  const isOverdue = new Date(invoice.dueDate) < new Date() && invoice.status !== 'PAID';

  const handleDownload = () => {
    console.log('Downloading invoice PDF:', invoice.id);
  };

  const handleSend = () => {
    console.log('Sending invoice:', invoice.id);
  };

  const handleEdit = () => {
    router.push(`/invoices/${invoiceId}/edit`);
  };

  const handlePrint = () => {
    window.print();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: invoice.currency
    }).format(amount);
  };

  return (
    <div className="container mx-auto p-6 space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">{invoice.number}</h1>
          <p className="text-muted-foreground mt-1">
            {invoice.matter.reference} • {invoice.matter.title}
          </p>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant={getStatusColor(invoice.status)} className="flex items-center gap-1">
              {getStatusIcon(invoice.status)}
              {invoice.status}
            </Badge>
            {isOverdue && (
              <Badge variant="destructive">
                Overdue
              </Badge>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          {invoice.status === 'DRAFT' && (
            <Button variant="outline" size="sm" onClick={handleSend}>
              <Send className="h-4 w-4 mr-2" />
              Send Invoice
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(invoice.total)}</div>
            <p className="text-xs text-muted-foreground">
              Including VAT
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Issue Date</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {format(new Date(invoice.issueDate), 'MMM d')}
            </div>
            <p className="text-xs text-muted-foreground">
              {format(new Date(invoice.issueDate), 'yyyy')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Due Date</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${isOverdue ? 'text-red-600' : ''}`}>
              {format(new Date(invoice.dueDate), 'MMM d')}
            </div>
            <p className="text-xs text-muted-foreground">
              {format(new Date(invoice.dueDate), 'yyyy')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {invoice.lineItems.reduce((sum, item) => sum + item.hours, 0)}h
            </div>
            <p className="text-xs text-muted-foreground">
              Billable time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Invoice Details */}
      <Card className="print:shadow-none">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">INVOICE</CardTitle>
              <CardDescription className="text-lg mt-2">
                {invoice.number}
              </CardDescription>
            </div>
            <div className="text-right">
              <h3 className="font-bold text-lg">{invoice.firm.name}</h3>
              <div className="text-sm text-muted-foreground whitespace-pre-line">
                {invoice.firm.address}
              </div>
              <div className="text-sm text-muted-foreground mt-2">
                <p>Phone: {invoice.firm.phone}</p>
                <p>Email: {invoice.firm.email}</p>
                <p>VAT: {invoice.firm.vatNumber}</p>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Bill To & Invoice Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Bill To:</h4>
              <div className="text-sm">
                <p className="font-medium">{invoice.client.name}</p>
                <div className="text-muted-foreground whitespace-pre-line">
                  {invoice.client.address}
                </div>
                <p className="text-muted-foreground mt-2">
                  Phone: {invoice.client.phone}
                </p>
                <p className="text-muted-foreground">
                  Email: {invoice.client.email}
                </p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Invoice Details:</h4>
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Matter:</span>
                  <span>{invoice.matter.reference}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Issue Date:</span>
                  <span>{format(new Date(invoice.issueDate), 'PPP')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Due Date:</span>
                  <span>{format(new Date(invoice.dueDate), 'PPP')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created By:</span>
                  <span>{invoice.createdBy.name}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Line Items */}
          <div>
            <h4 className="font-semibold mb-4">Services Provided:</h4>
            <div className="space-y-2">
              <div className="grid grid-cols-12 gap-2 text-sm font-medium border-b pb-2">
                <div className="col-span-2">Date</div>
                <div className="col-span-5">Description</div>
                <div className="col-span-1 text-right">Hours</div>
                <div className="col-span-2 text-right">Rate</div>
                <div className="col-span-2 text-right">Amount</div>
              </div>
              {invoice.lineItems.map((item) => (
                <div key={item.id} className="grid grid-cols-12 gap-2 text-sm py-2 border-b">
                  <div className="col-span-2 text-muted-foreground">
                    {format(new Date(item.date), 'MMM d, yyyy')}
                  </div>
                  <div className="col-span-5">{item.description}</div>
                  <div className="col-span-1 text-right">{item.hours}</div>
                  <div className="col-span-2 text-right">{formatCurrency(item.rate)}</div>
                  <div className="col-span-2 text-right font-medium">
                    {formatCurrency(item.amount)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Totals */}
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(invoice.subtotal)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>VAT ({(invoice.vatRate * 100).toFixed(0)}%):</span>
                <span>{formatCurrency(invoice.vatAmount)}</span>
              </div>
              <Separator />
              <div className="flex justify-between text-lg font-bold">
                <span>Total:</span>
                <span>{formatCurrency(invoice.total)}</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          {invoice.notes && (
            <>
              <Separator />
              <div>
                <h4 className="font-semibold mb-2">Notes:</h4>
                <p className="text-sm text-muted-foreground">{invoice.notes}</p>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
