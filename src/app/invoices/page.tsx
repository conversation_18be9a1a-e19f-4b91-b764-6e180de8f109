"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { 
  Receipt, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  FileText, 
  Download, 
  Send, 
  CreditCard, 
  Briefcase, 
  AlertCircle 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function InvoicesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [matterFilter, setMatterFilter] = useState("all");

  // Mock data for invoices
  const invoices = [
    {
      id: "1",
      number: "INV-2024-001",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      status: "PAID",
      issueDate: "2024-05-15",
      dueDate: "2024-06-14",
      subtotal: 2500,
      vatAmount: 375,
      total: 2875,
      paidAt: "2024-05-20",
    },
    {
      id: "2",
      number: "INV-2024-002",
      matterId: "2",
      matterTitle: "Johnson Estate Planning",
      status: "SENT",
      issueDate: "2024-05-20",
      dueDate: "2024-06-19",
      subtotal: 1800,
      vatAmount: 270,
      total: 2070,
      paidAt: null,
    },
    {
      id: "3",
      number: "INV-2024-003",
      matterId: "3",
      matterTitle: "ABC Corporation - Merger",
      status: "DRAFT",
      issueDate: "2024-06-01",
      dueDate: "2024-07-01",
      subtotal: 5000,
      vatAmount: 750,
      total: 5750,
      paidAt: null,
    },
    {
      id: "4",
      number: "INV-2024-004",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      status: "OVERDUE",
      issueDate: "2024-04-15",
      dueDate: "2024-05-15",
      subtotal: 1200,
      vatAmount: 180,
      total: 1380,
      paidAt: null,
    },
  ];

  // Filter invoices based on search query, status, and matter
  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      searchQuery === "" ||
      invoice.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.matterTitle.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || invoice.status.toLowerCase() === statusFilter.toLowerCase();

    const matchesMatter =
      matterFilter === "all" || invoice.matterId === matterFilter;

    return matchesSearch && matchesStatus && matchesMatter;
  });

  // Get status badge color
  const getStatusBadge = (status) => {
    switch (status) {
      case "PAID":
        return "bg-green-100 text-green-800";
      case "SENT":
        return "bg-blue-100 text-blue-800";
      case "DRAFT":
        return "bg-gray-100 text-gray-800";
      case "OVERDUE":
        return "bg-red-100 text-red-800";
      case "CANCELLED":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Invoices</h2>
        <Button onClick={() => router.push("/invoices/new")}>
          <Plus className="mr-2 h-4 w-4" />
          New Invoice
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Outstanding
            </CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 9,200</div>
            <p className="text-xs text-muted-foreground">
              3 unpaid invoices
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Overdue
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 1,380</div>
            <p className="text-xs text-muted-foreground">
              1 overdue invoice
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Paid This Month
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 2,875</div>
            <p className="text-xs text-muted-foreground">
              1 payment received
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Draft Invoices
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 5,750</div>
            <p className="text-xs text-muted-foreground">
              1 draft invoice
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search invoices..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
        <Select value={matterFilter} onValueChange={setMatterFilter}>
          <SelectTrigger className="w-[250px]">
            <SelectValue placeholder="Filter by Matter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Matters</SelectItem>
            <SelectItem value="1">Smith vs Jones - Contract Dispute</SelectItem>
            <SelectItem value="2">Johnson Estate Planning</SelectItem>
            <SelectItem value="3">ABC Corporation - Merger</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Invoice List</CardTitle>
          <CardDescription>
            Manage your client invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice #</TableHead>
                <TableHead>Matter</TableHead>
                <TableHead>Issue Date</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">{invoice.number}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{invoice.matterTitle}</span>
                    </div>
                  </TableCell>
                  <TableCell>{format(new Date(invoice.issueDate), "MMM d, yyyy")}</TableCell>
                  <TableCell>{format(new Date(invoice.dueDate), "MMM d, yyyy")}</TableCell>
                  <TableCell className="text-right">R {invoice.total.toFixed(2)}</TableCell>
                  <TableCell>
                    <div className={`rounded-full px-2 py-1 text-xs inline-block ${getStatusBadge(invoice.status)}`}>
                      {invoice.status}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button variant="ghost" size="icon">
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Download className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => router.push(`/invoices/${invoice.id}`)}>
                            View Details
                          </DropdownMenuItem>
                          {invoice.status === "DRAFT" && (
                            <DropdownMenuItem onClick={() => router.push(`/invoices/${invoice.id}/edit`)}>
                              Edit Invoice
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem>Download PDF</DropdownMenuItem>
                          {invoice.status === "DRAFT" && (
                            <DropdownMenuItem>
                              <Send className="mr-2 h-4 w-4" />
                              Send to Client
                            </DropdownMenuItem>
                          )}
                          {(invoice.status === "SENT" || invoice.status === "OVERDUE") && (
                            <DropdownMenuItem>
                              <CreditCard className="mr-2 h-4 w-4" />
                              Record Payment
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {invoice.status === "DRAFT" && (
                            <DropdownMenuItem className="text-destructive">
                              Delete Invoice
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
