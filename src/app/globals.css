@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .sidebar-nav {
    @apply flex h-full w-64 flex-col border-r bg-card;
  }
  
  .main-content {
    @apply flex-1 overflow-auto;
  }
  
  .page-header {
    @apply border-b bg-background px-6 py-4;
  }
  
  .page-title {
    @apply text-2xl font-bold tracking-tight;
  }
  
  .page-description {
    @apply text-muted-foreground;
  }
  
  .data-table {
    @apply w-full border-collapse border border-border;
  }
  
  .data-table th {
    @apply border border-border bg-muted px-4 py-2 text-left font-medium;
  }
  
  .data-table td {
    @apply border border-border px-4 py-2;
  }
  
  .form-section {
    @apply space-y-4 rounded-lg border bg-card p-6;
  }
  
  .form-section-title {
    @apply text-lg font-semibold;
  }
  
  .stats-card {
    @apply rounded-lg border bg-card p-6 shadow-sm;
  }
  
  .stats-card-title {
    @apply text-sm font-medium text-muted-foreground;
  }
  
  .stats-card-value {
    @apply text-2xl font-bold;
  }
  
  .kanban-column {
    @apply min-h-[500px] w-80 rounded-lg border bg-muted/50 p-4;
  }
  
  .kanban-card {
    @apply mb-3 cursor-pointer rounded border bg-card p-3 shadow-sm hover:shadow-md;
  }
  
  .invoice-header {
    @apply flex items-center justify-between border-b pb-4;
  }
  
  .invoice-details {
    @apply grid grid-cols-2 gap-8 py-6;
  }
  
  .invoice-table {
    @apply w-full border-collapse;
  }
  
  .invoice-table th,
  .invoice-table td {
    @apply border-b px-4 py-2 text-left;
  }
  
  .invoice-total {
    @apply border-t-2 border-primary bg-muted/50 px-4 py-2 font-semibold;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .text-balance {
    text-wrap: balance;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
  
  body {
    @apply text-black;
  }
  
  .invoice-header,
  .invoice-details,
  .invoice-table {
    @apply border-black;
  }
}