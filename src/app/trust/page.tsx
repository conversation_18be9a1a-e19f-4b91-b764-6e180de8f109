"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { 
  PiggyBank, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Briefcase, 
  FileText, 
  BarChart3 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";

export default function TrustPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [matterFilter, setMatterFilter] = useState("all");

  // Mock data for trust accounts
  const trustAccounts = [
    {
      id: "1",
      matterId: "1",
      matterTitle: "Smith vs Jones - Contract Dispute",
      name: "Smith Trust Account",
      balance: 50000,
      currency: "ZAR",
      createdAt: "2024-01-15",
    },
    {
      id: "2",
      matterId: "2",
      matterTitle: "Johnson Estate Planning",
      name: "Johnson Trust Account",
      balance: 75000,
      currency: "ZAR",
      createdAt: "2024-02-10",
    },
    {
      id: "3",
      matterId: "3",
      matterTitle: "ABC Corporation - Merger",
      name: "ABC Corp Trust Account",
      balance: 125000,
      currency: "ZAR",
      createdAt: "2024-03-05",
    },
  ];

  // Mock data for trust transfers
  const trustTransfers = [
    {
      id: "1",
      trustAccountId: "1",
      type: "DEPOSIT",
      amount: 50000,
      description: "Initial deposit",
      reference: "DEP-001",
      date: "2024-01-15",
      createdBy: "Jane Doe",
    },
    {
      id: "2",
      trustAccountId: "2",
      type: "DEPOSIT",
      amount: 100000,
      description: "Initial deposit",
      reference: "DEP-002",
      date: "2024-02-10",
      createdBy: "John Williams",
    },
    {
      id: "3",
      trustAccountId: "2",
      type: "WITHDRAWAL",
      amount: 25000,
      description: "Payment for legal fees",
      reference: "WIT-001",
      date: "2024-05-15",
      createdBy: "John Williams",
    },
    {
      id: "4",
      trustAccountId: "3",
      type: "DEPOSIT",
      amount: 150000,
      description: "Initial deposit",
      reference: "DEP-003",
      date: "2024-03-05",
      createdBy: "Sarah Johnson",
    },
    {
      id: "5",
      trustAccountId: "3",
      type: "WITHDRAWAL",
      amount: 25000,
      description: "Payment for legal fees",
      reference: "WIT-002",
      date: "2024-04-10",
      createdBy: "Sarah Johnson",
    },
    {
      id: "6",
      trustAccountId: "1",
      type: "INTEREST",
      amount: 250,
      description: "Monthly interest",
      reference: "INT-001",
      date: "2024-05-31",
      createdBy: "System",
    },
  ];

  // Filter trust accounts based on search query and matter
  const filteredTrustAccounts = trustAccounts.filter((account) => {
    const matchesSearch =
      searchQuery === "" ||
      account.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      account.matterTitle.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesMatter =
      matterFilter === "all" || account.matterId === matterFilter;

    return matchesSearch && matchesMatter;
  });

  // Filter trust transfers based on search query and matter
  const filteredTrustTransfers = trustTransfers.filter((transfer) => {
    const account = trustAccounts.find(a => a.id === transfer.trustAccountId);
    
    const matchesSearch =
      searchQuery === "" ||
      transfer.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      transfer.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (account && account.matterTitle.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesMatter =
      matterFilter === "all" || 
      (account && account.matterId === matterFilter);

    return matchesSearch && matchesMatter;
  });

  // Get transfer type badge color
  const getTransferTypeBadge = (type) => {
    switch (type) {
      case "DEPOSIT":
        return "bg-green-100 text-green-800";
      case "WITHDRAWAL":
        return "bg-red-100 text-red-800";
      case "TRANSFER":
        return "bg-blue-100 text-blue-800";
      case "INTEREST":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get transfer type icon
  const getTransferTypeIcon = (type) => {
    switch (type) {
      case "DEPOSIT":
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
      case "WITHDRAWAL":
        return <ArrowUpRight className="h-4 w-4 text-red-600" />;
      case "TRANSFER":
        return <ArrowUpRight className="h-4 w-4 text-blue-600" />;
      case "INTEREST":
        return <PiggyBank className="h-4 w-4 text-purple-600" />;
      default:
        return <ArrowDownLeft className="h-4 w-4" />;
    }
  };

  // Calculate total trust balance
  const totalTrustBalance = trustAccounts.reduce((sum, account) => sum + account.balance, 0);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Trust Accounting</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push("/trust/reconciliation")}>
            <FileText className="mr-2 h-4 w-4" />
            Reconciliation
          </Button>
          <Button variant="outline" onClick={() => router.push("/trust/reports")}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Reports
          </Button>
          <Button onClick={() => router.push("/trust/new-transfer")}>
            <Plus className="mr-2 h-4 w-4" />
            New Transfer
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Trust Balance
            </CardTitle>
            <PiggyBank className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R {totalTrustBalance.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Across {trustAccounts.length} accounts
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Deposits (30 days)
            </CardTitle>
            <ArrowDownLeft className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 50,250.00</div>
            <p className="text-xs text-muted-foreground">
              2 deposits
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Withdrawals (30 days)
            </CardTitle>
            <ArrowUpRight className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 25,000.00</div>
            <p className="text-xs text-muted-foreground">
              1 withdrawal
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Interest Earned (YTD)
            </CardTitle>
            <PiggyBank className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R 1,250.00</div>
            <p className="text-xs text-muted-foreground">
              5 interest payments
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search trust accounts or transactions..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={matterFilter} onValueChange={setMatterFilter}>
          <SelectTrigger className="w-[250px]">
            <SelectValue placeholder="Filter by Matter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Matters</SelectItem>
            <SelectItem value="1">Smith vs Jones - Contract Dispute</SelectItem>
            <SelectItem value="2">Johnson Estate Planning</SelectItem>
            <SelectItem value="3">ABC Corporation - Merger</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue="accounts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="accounts">Trust Accounts</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        <TabsContent value="accounts">
          <Card>
            <CardHeader>
              <CardTitle>Trust Accounts</CardTitle>
              <CardDescription>
                Manage client trust accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Account Name</TableHead>
                    <TableHead>Matter</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Balance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTrustAccounts.map((account) => (
                    <TableRow key={account.id}>
                      <TableCell className="font-medium">{account.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>{account.matterTitle}</span>
                        </div>
                      </TableCell>
                      <TableCell>{format(new Date(account.createdAt), "MMM d, yyyy")}</TableCell>
                      <TableCell className="text-right">R {account.balance.toFixed(2)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.push(`/trust/${account.id}`)}>
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/trust/${account.id}/statement`)}>
                              Generate Statement
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/trust/new-transfer?accountId=${account.id}&type=deposit`)}>
                              <ArrowDownLeft className="mr-2 h-4 w-4 text-green-600" />
                              Add Deposit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/trust/new-transfer?accountId=${account.id}&type=withdrawal`)}>
                              <ArrowUpRight className="mr-2 h-4 w-4 text-red-600" />
                              Add Withdrawal
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Trust Transactions</CardTitle>
              <CardDescription>
                View all trust account transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Account</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTrustTransfers.map((transfer) => {
                    const account = trustAccounts.find(a => a.id === transfer.trustAccountId);
                    return (
                      <TableRow key={transfer.id}>
                        <TableCell>{format(new Date(transfer.date), "MMM d, yyyy")}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getTransferTypeIcon(transfer.type)}
                            <span className={`rounded-full px-2 py-1 text-xs ${getTransferTypeBadge(transfer.type)}`}>
                              {transfer.type}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{transfer.description}</TableCell>
                        <TableCell>{transfer.reference}</TableCell>
                        <TableCell>
                          {account ? account.name : "Unknown Account"}
                        </TableCell>
                        <TableCell className="text-right">
                          <span className={transfer.type === "WITHDRAWAL" ? "text-red-600" : ""}>
                            R {transfer.amount.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => router.push(`/trust/transfers/${transfer.id}`)}>
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>Print Receipt</DropdownMenuItem>
                              {transfer.type !== "INTEREST" && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-destructive">
                                    Void Transaction
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
