import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Providers } from "@/components/providers";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Practice Manager - Legal Practice Management System",
  description: "A comprehensive practice management platform for legal professionals",
  keywords: ["legal", "practice management", "law firm", "billing", "time tracking", "trust accounting"],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}