"use client";

import { Scale } from "lucide-react";
import { cn } from "@/lib/utils";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  showText?: boolean;
  textClassName?: string;
}

export function Logo({
  className,
  size = "md",
  showText = true,
  textClassName,
}: LogoProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-10 w-10",
  };

  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl",
  };

  return (
    <div className="flex items-center space-x-2">
      <div
        className={cn(
          "flex items-center justify-center rounded-md bg-primary p-1 text-primary-foreground",
          sizeClasses[size],
          className
        )}
      >
        <Scale className="h-full w-full" />
      </div>
      {showText && (
        <span
          className={cn(
            "font-bold tracking-tight",
            textSizeClasses[size],
            textClassName
          )}
        >
          Practice Manager
        </span>
      )}
    </div>
  );
}
