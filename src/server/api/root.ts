import { createTR<PERSON><PERSON>outer } from "./trpc";
import { userRouter } from "./routers/user";
import { matterRouter } from "./routers/matter";
import { timeRouter } from "./routers/time";
import { taskRouter } from "./routers/task";
import { documentRouter } from "./routers/document";
import { invoiceRouter } from "./routers/invoice";
import { trustRouter } from "./routers/trust";
import { reportRouter } from "./routers/report";
import { notificationRouter } from "./routers/notification";
import { courtTariffRouter } from "./routers/court-tariff";
import { permissionRouter } from "./routers/permission";
import { roleRouter } from "./routers/role";
import { processRouter } from "./routers/process";
import { clientRouter } from "./routers/client";
import { systemRouter } from "./routers/system";

export const appRouter = createTRPCRouter({
  user: userRouter,
  matter: matterRouter,
  time: timeRouter,
  task: taskRouter,
  document: documentRouter,
  invoice: invoiceRouter,
  trust: trustRouter,
  report: reportRouter,
  notification: notificationRouter,
  courtTariff: courtTariffRouter,
  permission: permissionRouter,
  role: roleRouter,
  process: processRouter,
  client: clientRouter,
  system: systemRouter,
});

export type AppRouter = typeof appRouter;