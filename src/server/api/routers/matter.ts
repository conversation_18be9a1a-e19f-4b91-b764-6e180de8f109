import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, attorneyProcedure } from "../trpc";
import { MatterStatus, PartyType } from "@prisma/client";

export const matterRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        search: z.string().optional(),
        status: z.nativeEnum(MatterStatus).optional(),
        userId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, search, status, userId } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(search && {
          OR: [
            { reference: { contains: search, mode: "insensitive" as const } },
            { title: { contains: search, mode: "insensitive" as const } },
            { clientName: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(status && { status }),
        ...(userId && { userId }),
      };

      const [matters, total] = await Promise.all([
        ctx.prisma.matter.findMany({
          where,
          include: {
            user: {
              select: { id: true, name: true, email: true },
            },
            createdBy: {
              select: { id: true, name: true, email: true },
            },
            parties: true,
            _count: {
              select: {
                documents: true,
                timeEntries: true,
                tasks: true,
              },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        ctx.prisma.matter.count({ where }),
      ]);

      return {
        matters,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const matter = await ctx.prisma.matter.findUnique({
        where: { id: input.id },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          parties: true,
          documents: {
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
            orderBy: { createdAt: "desc" },
          },
          timeEntries: {
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
            orderBy: { date: "desc" },
          },
          tasks: {
            include: {
              user: {
                select: { id: true, name: true },
              },
              assigned: {
                select: { id: true, name: true },
              },
            },
            orderBy: { createdAt: "desc" },
          },
          trustAccount: true,
        },
      });

      if (!matter) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Matter not found" });
      }

      return matter;
    }),

  create: attorneyProcedure
    .input(
      z.object({
        reference: z.string().min(1),
        title: z.string().min(1),
        description: z.string().optional(),
        clientName: z.string().min(1),
        clientEmail: z.string().email().optional(),
        clientPhone: z.string().optional(),
        stage: z.string().optional(),
        customFields: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const existingMatter = await ctx.prisma.matter.findUnique({
        where: { reference: input.reference },
      });

      if (existingMatter) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Matter with this reference already exists",
        });
      }

      const matter = await ctx.prisma.matter.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
          createdById: ctx.session.user.id,
        },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          parties: true,
        },
      });

      return matter;
    }),

  update: attorneyProcedure
    .input(
      z.object({
        id: z.string(),
        reference: z.string().min(1).optional(),
        title: z.string().min(1).optional(),
        description: z.string().optional(),
        status: z.nativeEnum(MatterStatus).optional(),
        clientName: z.string().min(1).optional(),
        clientEmail: z.string().email().optional(),
        clientPhone: z.string().optional(),
        stage: z.string().optional(),
        customFields: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const matter = await ctx.prisma.matter.update({
        where: { id },
        data,
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          parties: true,
        },
      });

      return matter;
    }),

  delete: attorneyProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.matter.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  addParty: attorneyProcedure
    .input(
      z.object({
        matterId: z.string(),
        name: z.string().min(1),
        type: z.nativeEnum(PartyType),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        address: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const party = await ctx.prisma.party.create({
        data: input,
      });

      return party;
    }),

  updateParty: attorneyProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        type: z.nativeEnum(PartyType).optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        address: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const party = await ctx.prisma.party.update({
        where: { id },
        data,
      });

      return party;
    }),

  deleteParty: attorneyProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.party.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  getStats: protectedProcedure.query(async ({ ctx }) => {
    const [total, active, closed, archived] = await Promise.all([
      ctx.prisma.matter.count(),
      ctx.prisma.matter.count({ where: { status: "ACTIVE" } }),
      ctx.prisma.matter.count({ where: { status: "CLOSED" } }),
      ctx.prisma.matter.count({ where: { status: "ARCHIVED" } }),
    ]);

    return {
      total,
      active,
      closed,
      archived,
    };
  }),
});