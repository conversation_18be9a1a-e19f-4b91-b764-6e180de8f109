import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { TaskStatus, Priority } from "@prisma/client";

export const taskRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        matterId: z.string().optional(),
        status: z.nativeEnum(TaskStatus).optional(),
        assignedId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, matterId, status, assignedId } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(matterId && { matterId }),
        ...(status && { status }),
        ...(assignedId && { assignedId }),
      };

      const [tasks, total] = await Promise.all([
        ctx.prisma.task.findMany({
          where,
          include: {
            matter: {
              select: { id: true, reference: true, title: true },
            },
            user: {
              select: { id: true, name: true },
            },
            assigned: {
              select: { id: true, name: true },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        ctx.prisma.task.count({ where }),
      ]);

      return {
        tasks,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  create: protectedProcedure
    .input(
      z.object({
        matterId: z.string().optional(),
        assignedId: z.string().optional(),
        title: z.string().min(1),
        description: z.string().optional(),
        priority: z.nativeEnum(Priority).default("MEDIUM"),
        dueDate: z.date().optional(),
        isRecurring: z.boolean().default(false),
        recurringPattern: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const task = await ctx.prisma.task.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
          assigned: {
            select: { id: true, name: true },
          },
        },
      });

      return task;
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1).optional(),
        description: z.string().optional(),
        status: z.nativeEnum(TaskStatus).optional(),
        priority: z.nativeEnum(Priority).optional(),
        dueDate: z.date().optional(),
        assignedId: z.string().optional(),
        completedAt: z.date().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const task = await ctx.prisma.task.update({
        where: { id },
        data,
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
          assigned: {
            select: { id: true, name: true },
          },
        },
      });

      return task;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.task.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  getKanban: protectedProcedure
    .input(
      z.object({
        matterId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const where = {
        ...(input.matterId && { matterId: input.matterId }),
      };

      const tasks = await ctx.prisma.task.findMany({
        where,
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
          assigned: {
            select: { id: true, name: true },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      const kanban = {
        TODO: tasks.filter((task) => task.status === "TODO"),
        IN_PROGRESS: tasks.filter((task) => task.status === "IN_PROGRESS"),
        COMPLETED: tasks.filter((task) => task.status === "COMPLETED"),
        CANCELLED: tasks.filter((task) => task.status === "CANCELLED"),
      };

      return kanban;
    }),

  getStats: protectedProcedure.query(async ({ ctx }) => {
    const [total, todo, inProgress, completed, overdue] = await Promise.all([
      ctx.prisma.task.count(),
      ctx.prisma.task.count({ where: { status: "TODO" } }),
      ctx.prisma.task.count({ where: { status: "IN_PROGRESS" } }),
      ctx.prisma.task.count({ where: { status: "COMPLETED" } }),
      ctx.prisma.task.count({
        where: {
          status: { in: ["TODO", "IN_PROGRESS"] },
          dueDate: { lt: new Date() },
        },
      }),
    ]);

    return {
      total,
      todo,
      inProgress,
      completed,
      overdue,
    };
  }),
});