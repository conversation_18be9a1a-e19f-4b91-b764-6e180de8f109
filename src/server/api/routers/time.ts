import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const timeRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        matterId: z.string().optional(),
        userId: z.string().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, matterId, userId, startDate, endDate } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(matterId && { matterId }),
        ...(userId && { userId }),
        ...(startDate && endDate && {
          date: {
            gte: startDate,
            lte: endDate,
          },
        }),
      };

      const [timeEntries, total] = await Promise.all([
        ctx.prisma.timeEntry.findMany({
          where,
          include: {
            matter: {
              select: { id: true, reference: true, title: true },
            },
            user: {
              select: { id: true, name: true },
            },
          },
          skip,
          take: limit,
          orderBy: { date: "desc" },
        }),
        ctx.prisma.timeEntry.count({ where }),
      ]);

      return {
        timeEntries,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  create: protectedProcedure
    .input(
      z.object({
        matterId: z.string(),
        description: z.string().min(1),
        hours: z.number().positive(),
        rate: z.number().positive(),
        date: z.date(),
        isBillable: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const amount = input.hours * input.rate;

      const timeEntry = await ctx.prisma.timeEntry.create({
        data: {
          ...input,
          amount,
          userId: ctx.session.user.id,
        },
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
        },
      });

      return timeEntry;
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        description: z.string().min(1).optional(),
        hours: z.number().positive().optional(),
        rate: z.number().positive().optional(),
        date: z.date().optional(),
        isBillable: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, hours, rate, ...data } = input;

      // Recalculate amount if hours or rate changed
      const updateData = {
        ...data,
        ...(hours && { hours }),
        ...(rate && { rate }),
        ...(hours && rate && { amount: hours * rate }),
      };

      const timeEntry = await ctx.prisma.timeEntry.update({
        where: { id },
        data: updateData,
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
        },
      });

      return timeEntry;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.timeEntry.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  getStats: protectedProcedure
    .input(
      z.object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        userId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { startDate, endDate, userId } = input;

      const where = {
        ...(userId && { userId }),
        ...(startDate && endDate && {
          date: {
            gte: startDate,
            lte: endDate,
          },
        }),
      };

      const [totalHours, billableHours, totalAmount] = await Promise.all([
        ctx.prisma.timeEntry.aggregate({
          where,
          _sum: { hours: true },
        }),
        ctx.prisma.timeEntry.aggregate({
          where: { ...where, isBillable: true },
          _sum: { hours: true },
        }),
        ctx.prisma.timeEntry.aggregate({
          where: { ...where, isBillable: true },
          _sum: { amount: true },
        }),
      ]);

      return {
        totalHours: totalHours._sum.hours || 0,
        billableHours: billableHours._sum.hours || 0,
        totalAmount: totalAmount._sum.amount || 0,
      };
    }),
});