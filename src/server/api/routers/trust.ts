import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { TrustTransferType } from "@prisma/client";

export const trustRouter = createTRPCRouter({
  getAccounts: protectedProcedure
    .input(
      z.object({
        matterId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const where = {
        ...(input.matterId && { matterId: input.matterId }),
      };

      const accounts = await ctx.prisma.trustAccount.findMany({
        where,
        include: {
          matter: {
            select: { id: true, reference: true, title: true, clientName: true },
          },
          transfers: {
            orderBy: { date: "desc" },
            take: 5,
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return accounts;
    }),

  getAccountById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const account = await ctx.prisma.trustAccount.findUnique({
        where: { id: input.id },
        include: {
          matter: {
            select: { id: true, reference: true, title: true, clientName: true },
          },
          transfers: {
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
            orderBy: { date: "desc" },
          },
        },
      });

      if (!account) {
        throw new Error("Trust account not found");
      }

      return account;
    }),

  createAccount: protectedProcedure
    .input(
      z.object({
        matterId: z.string(),
        name: z.string().min(1),
        currency: z.string().default("ZAR"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.prisma.trustAccount.create({
        data: input,
        include: {
          matter: {
            select: { id: true, reference: true, title: true, clientName: true },
          },
        },
      });

      return account;
    }),

  getTransfers: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        trustAccountId: z.string().optional(),
        type: z.nativeEnum(TrustTransferType).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, trustAccountId, type, startDate, endDate } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(trustAccountId && { trustAccountId }),
        ...(type && { type }),
        ...(startDate && endDate && {
          date: {
            gte: startDate,
            lte: endDate,
          },
        }),
      };

      const [transfers, total] = await Promise.all([
        ctx.prisma.trustTransfer.findMany({
          where,
          include: {
            trustAccount: {
              include: {
                matter: {
                  select: { id: true, reference: true, title: true },
                },
              },
            },
            user: {
              select: { id: true, name: true },
            },
          },
          skip,
          take: limit,
          orderBy: { date: "desc" },
        }),
        ctx.prisma.trustTransfer.count({ where }),
      ]);

      return {
        transfers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  createTransfer: protectedProcedure
    .input(
      z.object({
        trustAccountId: z.string(),
        type: z.nativeEnum(TrustTransferType),
        amount: z.number().positive(),
        description: z.string().min(1),
        reference: z.string().optional(),
        date: z.date().default(() => new Date()),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { trustAccountId, type, amount } = input;

      // Get current account balance
      const account = await ctx.prisma.trustAccount.findUnique({
        where: { id: trustAccountId },
      });

      if (!account) {
        throw new Error("Trust account not found");
      }

      // Check if withdrawal would result in negative balance
      if (type === "WITHDRAWAL" && Number(account.balance) < amount) {
        throw new Error("Insufficient funds in trust account");
      }

      // Calculate new balance
      let newBalance = Number(account.balance);
      if (type === "DEPOSIT") {
        newBalance += amount;
      } else if (type === "WITHDRAWAL") {
        newBalance -= amount;
      }

      // Create transfer and update balance in transaction
      const [transfer] = await ctx.prisma.$transaction([
        ctx.prisma.trustTransfer.create({
          data: {
            ...input,
            userId: ctx.session.user.id,
          },
          include: {
            trustAccount: {
              include: {
                matter: {
                  select: { id: true, reference: true, title: true },
                },
              },
            },
            user: {
              select: { id: true, name: true },
            },
          },
        }),
        ctx.prisma.trustAccount.update({
          where: { id: trustAccountId },
          data: { balance: newBalance },
        }),
      ]);

      return transfer;
    }),

  reconcile: protectedProcedure
    .input(
      z.object({
        trustAccountId: z.string(),
        bankBalance: z.number(),
        reconciliationDate: z.date(),
        notes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { trustAccountId, bankBalance, reconciliationDate } = input;

      const account = await ctx.prisma.trustAccount.findUnique({
        where: { id: trustAccountId },
      });

      if (!account) {
        throw new Error("Trust account not found");
      }

      const bookBalance = Number(account.balance);
      const difference = bankBalance - bookBalance;

      // If there's a difference, create an adjustment transfer
      if (Math.abs(difference) > 0.01) {
        const adjustmentType = difference > 0 ? "DEPOSIT" : "WITHDRAWAL";
        const adjustmentAmount = Math.abs(difference);

        await ctx.prisma.trustTransfer.create({
          data: {
            trustAccountId,
            type: adjustmentType,
            amount: adjustmentAmount,
            description: `Reconciliation adjustment - ${reconciliationDate.toISOString().split('T')[0]}`,
            reference: `RECON-${Date.now()}`,
            date: reconciliationDate,
            userId: ctx.session.user.id,
          },
        });

        // Update account balance
        await ctx.prisma.trustAccount.update({
          where: { id: trustAccountId },
          data: { balance: bankBalance },
        });
      }

      return {
        bookBalance,
        bankBalance,
        difference,
        reconciled: Math.abs(difference) <= 0.01,
      };
    }),

  getStats: protectedProcedure.query(async ({ ctx }) => {
    const [totalAccounts, totalBalance, deposits, withdrawals] = await Promise.all([
      ctx.prisma.trustAccount.count(),
      ctx.prisma.trustAccount.aggregate({
        _sum: { balance: true },
      }),
      ctx.prisma.trustTransfer.aggregate({
        where: { type: "DEPOSIT" },
        _sum: { amount: true },
      }),
      ctx.prisma.trustTransfer.aggregate({
        where: { type: "WITHDRAWAL" },
        _sum: { amount: true },
      }),
    ]);

    return {
      totalAccounts,
      totalBalance: totalBalance._sum.balance || 0,
      totalDeposits: deposits._sum.amount || 0,
      totalWithdrawals: withdrawals._sum.amount || 0,
    };
  }),
});