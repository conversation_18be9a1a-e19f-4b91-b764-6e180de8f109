import { z } from "zod";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "../trpc";
import { UserRole } from "@prisma/client";

export const userRouter = createTRPCRouter({
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.prisma.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        mfaEnabled: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
    }

    return user;
  }),

  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1).optional(),
        email: z.string().email().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const updatedUser = await ctx.prisma.user.update({
        where: { id: ctx.session.user.id },
        data: input,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          mfaEnabled: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return updatedUser;
    }),

  changePassword: protectedProcedure
    .input(
      z.object({
        currentPassword: z.string().min(1),
        newPassword: z.string().min(8),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.prisma.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { password: true },
      });

      if (!user?.password) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "User has no password set",
        });
      }

      const isCurrentPasswordValid = await bcrypt.compare(
        input.currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Current password is incorrect",
        });
      }

      const hashedNewPassword = await bcrypt.hash(input.newPassword, 12);

      await ctx.prisma.user.update({
        where: { id: ctx.session.user.id },
        data: { password: hashedNewPassword },
      });

      return { success: true };
    }),

  getAll: adminProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        search: z.string().optional(),
        role: z.nativeEnum(UserRole).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, search, role } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { email: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(role && { role }),
      };

      const [users, total] = await Promise.all([
        ctx.prisma.user.findMany({
          where,
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isActive: true,
            mfaEnabled: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        ctx.prisma.user.count({ where }),
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  create: adminProcedure
    .input(
      z.object({
        name: z.string().min(1),
        email: z.string().email(),
        password: z.string().min(8),
        role: z.nativeEnum(UserRole),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const existingUser = await ctx.prisma.user.findUnique({
        where: { email: input.email },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User with this email already exists",
        });
      }

      const hashedPassword = await bcrypt.hash(input.password, 12);

      const user = await ctx.prisma.user.create({
        data: {
          name: input.name,
          email: input.email,
          password: hashedPassword,
          role: input.role,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          mfaEnabled: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return user;
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        email: z.string().email().optional(),
        role: z.nativeEnum(UserRole).optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const user = await ctx.prisma.user.update({
        where: { id },
        data,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          mfaEnabled: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return user;
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.user.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  getBillableRates: protectedProcedure.query(async ({ ctx }) => {
    const rates = await ctx.prisma.billableRate.findMany({
      where: { userId: ctx.session.user.id },
      orderBy: { createdAt: "desc" },
    });

    return rates;
  }),

  updateBillableRate: protectedProcedure
    .input(
      z.object({
        rate: z.number().positive(),
        currency: z.string().default("ZAR"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Set all existing rates to non-default
      await ctx.prisma.billableRate.updateMany({
        where: { userId: ctx.session.user.id },
        data: { isDefault: false },
      });

      // Create new default rate
      const billableRate = await ctx.prisma.billableRate.create({
        data: {
          userId: ctx.session.user.id,
          rate: input.rate,
          currency: input.currency,
          isDefault: true,
        },
      });

      return billableRate;
    }),

  getById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const user = await ctx.prisma.user.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          role: true,
          roleId: true,
          isActive: true,
          mfaEnabled: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          customRole: {
            select: {
              id: true,
              name: true,
              description: true,
              isSystem: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return user;
    }),

  getPermissions: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async () => {
      try {
        // Return mock permissions for now
        return [
          {
            id: "1",
            code: "user:view",
            name: "View Users",
            description: "Can view user profiles",
            category: "User Management",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "2",
            code: "matter:view",
            name: "View Matters",
            description: "Can view matters",
            category: "Matter Management",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "3",
            code: "document:view",
            name: "View Documents",
            description: "Can view documents",
            category: "Document Management",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "4",
            code: "billing:view",
            name: "View Billing",
            description: "Can view billing information",
            category: "Billing",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ];
      } catch (error) {
        console.error("Error in getPermissions:", error);
        return [];
      }
    }),

  updateRole: adminProcedure
    .input(
      z.object({
        id: z.string(),
        roleId: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        // Mock success response
        return {
          id: input.id,
          name: "User Name",
          email: "<EMAIL>",
          role: "ATTORNEY",
          isActive: true,
        };
      } catch (error) {
        console.error("Error in updateRole:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user role",
        });
      }
    }),

  updatePermissions: adminProcedure
    .input(
      z.object({
        id: z.string(),
        permissionIds: z.array(z.string()),
      })
    )
    .mutation(async () => {
      try {
        // Mock success response
        return { success: true };
      } catch (error) {
        console.error("Error in updatePermissions:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user permissions",
        });
      }
    }),
});