import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "../trpc";

export const roleRouter = createTRPCRouter({
  getAll: adminProcedure.query(async ({ ctx }) => {
    try {
      const roles = await ctx.prisma.role.findMany({
        include: {
          _count: {
            select: {
              users: true,
              rolePermissions: true,
            },
          },
        },
        orderBy: { name: "asc" },
      });

      return roles;
    } catch (error) {
      console.error("Error in getAll:", error);

      // Return mock data if there's an error
      return [
        {
          id: "1",
          name: "Administrator",
          description: "Full access to all system features",
          isSystem: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          _count: {
            users: 1,
            rolePermissions: 24,
          },
        },
        {
          id: "2",
          name: "Attorney",
          description: "Access to matters, billing, and documents",
          isSystem: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          _count: {
            users: 1,
            rolePermissions: 12,
          },
        },
        {
          id: "3",
          name: "Secretary",
          description: "Limited access to matters and documents",
          isSystem: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          _count: {
            users: 1,
            rolePermissions: 6,
          },
        },
        {
          id: "4",
          name: "Bookkeeper",
          description: "Access to billing and trust accounting",
          isSystem: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          _count: {
            users: 1,
            rolePermissions: 8,
          },
        },
      ];
    }
  }),

  getById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      // Mock roles data
      const mockRoles = {
        "1": {
          id: "1",
          name: "Administrator",
          description: "Full access to all system features",
          isSystem: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          rolePermissions: [
            {
              id: "1",
              roleId: "1",
              permissionId: "1",
              createdAt: new Date(),
              permission: {
                id: "1",
                code: "user:view",
                name: "View Users",
                description: "Can view user profiles",
                category: "User Management",
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
            {
              id: "2",
              roleId: "1",
              permissionId: "2",
              createdAt: new Date(),
              permission: {
                id: "2",
                code: "user:create",
                name: "Create Users",
                description: "Can create new users",
                category: "User Management",
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
          ],
          _count: {
            users: 1,
          },
        },
        "2": {
          id: "2",
          name: "Attorney",
          description: "Access to matters, billing, and documents",
          isSystem: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          rolePermissions: [
            {
              id: "3",
              roleId: "2",
              permissionId: "5",
              createdAt: new Date(),
              permission: {
                id: "5",
                code: "matter:view",
                name: "View Matters",
                description: "Can view matters",
                category: "Matter Management",
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
          ],
          _count: {
            users: 1,
          },
        },
      };

      const role = mockRoles[input.id as keyof typeof mockRoles];

      if (!role) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Role not found",
        });
      }

      return role;
    }),

  create: adminProcedure
    .input(
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        permissionIds: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ input }) => {
      // Mock creating a role
      return {
        id: "new-role-id",
        name: input.name,
        description: input.description,
        isSystem: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        rolePermissions: input.permissionIds?.map((permissionId, index) => ({
          id: `rp-${index}`,
          roleId: "new-role-id",
          permissionId,
          createdAt: new Date(),
          permission: {
            id: permissionId,
            code: `permission:${permissionId}`,
            name: `Permission ${permissionId}`,
            description: "Permission description",
            category: "User Management",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        })) || [],
      };
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { id, ...data } = input;

      // Mock updating a role
      if (id === "1" || id === "2") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot modify system roles",
        });
      }

      return {
        id,
        name: data.name || "Role Name",
        description: data.description || "Role Description",
        isSystem: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        rolePermissions: [
          {
            id: "rp-1",
            roleId: id,
            permissionId: "1",
            createdAt: new Date(),
            permission: {
              id: "1",
              code: "user:view",
              name: "View Users",
              description: "Can view user profiles",
              category: "User Management",
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        ],
      };
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      // Mock deleting a role
      if (input.id === "1" || input.id === "2") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete system roles",
        });
      }

      return { success: true };
    }),

  updatePermissions: adminProcedure
    .input(
      z.object({
        id: z.string(),
        permissionIds: z.array(z.string()),
      })
    )
    .mutation(async ({ input }) => {
      const { id, permissionIds } = input;

      // Mock updating role permissions
      if (id === "1" || id === "2") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot modify system roles",
        });
      }

      return {
        id,
        name: "Role Name",
        description: "Role Description",
        isSystem: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        rolePermissions: permissionIds.map((permissionId, index) => ({
          id: `rp-${index}`,
          roleId: id,
          permissionId,
          createdAt: new Date(),
          permission: {
            id: permissionId,
            code: `permission:${permissionId}`,
            name: `Permission ${permissionId}`,
            description: "Permission description",
            category: "User Management",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        })),
      };
    }),
});
