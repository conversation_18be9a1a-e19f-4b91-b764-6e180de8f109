import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { NotificationType } from "@prisma/client";

export const notificationRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        isRead: z.boolean().optional(),
        type: z.nativeEnum(NotificationType).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, isRead, type } = input;
      const skip = (page - 1) * limit;

      const where = {
        userId: ctx.session.user.id,
        ...(typeof isRead === "boolean" && { isRead }),
        ...(type && { type }),
      };

      const [notifications, total, unreadCount] = await Promise.all([
        ctx.prisma.notification.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        ctx.prisma.notification.count({ where }),
        ctx.prisma.notification.count({
          where: { userId: ctx.session.user.id, isRead: false },
        }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        unreadCount,
      };
    }),

  markAsRead: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const notification = await ctx.prisma.notification.update({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
        data: { isRead: true },
      });

      return notification;
    }),

  markAllAsRead: protectedProcedure.mutation(async ({ ctx }) => {
    await ctx.prisma.notification.updateMany({
      where: {
        userId: ctx.session.user.id,
        isRead: false,
      },
      data: { isRead: true },
    });

    return { success: true };
  }),

  create: protectedProcedure
    .input(
      z.object({
        userId: z.string().optional(),
        type: z.nativeEnum(NotificationType),
        title: z.string().min(1),
        message: z.string().min(1),
        data: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const notification = await ctx.prisma.notification.create({
        data: {
          ...input,
          userId: input.userId || ctx.session.user.id,
        },
      });

      return notification;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.notification.delete({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      });

      return { success: true };
    }),

  getUnreadCount: protectedProcedure.query(async ({ ctx }) => {
    const count = await ctx.prisma.notification.count({
      where: {
        userId: ctx.session.user.id,
        isRead: false,
      },
    });

    return count;
  }),
});