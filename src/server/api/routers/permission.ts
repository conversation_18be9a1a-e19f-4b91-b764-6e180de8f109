import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "../trpc";

export const permissionRouter = createTRPCRouter({
  getAll: adminProcedure.query(async () => {
    // Return mock permissions
    return [
      {
        id: "1",
        code: "user:view",
        name: "View Users",
        description: "Can view user profiles",
        category: "User Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "2",
        code: "user:create",
        name: "Create Users",
        description: "Can create new users",
        category: "User Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "3",
        code: "user:edit",
        name: "Edit Users",
        description: "Can edit user profiles",
        category: "User Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "4",
        code: "user:delete",
        name: "Delete Users",
        description: "Can delete users",
        category: "User Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "5",
        code: "matter:view",
        name: "View Matters",
        description: "Can view matters",
        category: "Matter Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "6",
        code: "matter:create",
        name: "Create Matters",
        description: "Can create new matters",
        category: "Matter Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "7",
        code: "document:view",
        name: "View Documents",
        description: "Can view documents",
        category: "Document Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "8",
        code: "document:create",
        name: "Create Documents",
        description: "Can create new documents",
        category: "Document Management",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
  }),

  getById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      // Return mock permission based on id
      const mockPermissions = {
        "1": {
          id: "1",
          code: "user:view",
          name: "View Users",
          description: "Can view user profiles",
          category: "User Management",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        "2": {
          id: "2",
          code: "user:create",
          name: "Create Users",
          description: "Can create new users",
          category: "User Management",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      const permission = mockPermissions[input.id as keyof typeof mockPermissions];

      if (!permission) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Permission not found",
        });
      }

      return permission;
    }),

  getByCategory: adminProcedure
    .input(z.object({ category: z.string() }))
    .query(async ({ input }) => {
      // Return mock permissions filtered by category
      const allPermissions = [
        {
          id: "1",
          code: "user:view",
          name: "View Users",
          description: "Can view user profiles",
          category: "User Management",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "2",
          code: "user:create",
          name: "Create Users",
          description: "Can create new users",
          category: "User Management",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "5",
          code: "matter:view",
          name: "View Matters",
          description: "Can view matters",
          category: "Matter Management",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "6",
          code: "matter:create",
          name: "Create Matters",
          description: "Can create new matters",
          category: "Matter Management",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      return allPermissions.filter(p => p.category === input.category);
    }),

  getCategories: adminProcedure.query(async () => {
    // Return mock categories
    return [
      "User Management",
      "Matter Management",
      "Document Management",
      "Billing",
      "Trust Accounting",
      "System Administration"
    ];
  }),

  create: adminProcedure
    .input(
      z.object({
        code: z.string().min(1),
        name: z.string().min(1),
        description: z.string().optional(),
        category: z.string().min(1),
      })
    )
    .mutation(async ({ input }) => {
      // Mock creating a permission
      return {
        id: "new-id",
        code: input.code,
        name: input.name,
        description: input.description,
        category: input.category,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        category: z.string().min(1).optional(),
      })
    )
    .mutation(async ({ input }) => {
      // Mock updating a permission
      const { id, ...data } = input;

      return {
        id,
        code: "permission:code",
        name: data.name || "Permission Name",
        description: data.description || "Permission Description",
        category: data.category || "Permission Category",
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async () => {
      // Mock deleting a permission
      return { success: true };
    }),
});
