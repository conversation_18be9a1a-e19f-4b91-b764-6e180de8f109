import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "../trpc";
import { SystemStatus } from "@prisma/client";

export const systemRouter = createTRPCRouter({
  getStatus: protectedProcedure.query(async ({ ctx }) => {
    try {
      // Mock data for system status since we don't have the actual tables yet
      const mockComponents = [
        {
          id: "1",
          component: "Database",
          status: "NORMAL",
          message: "Database is running normally",
          createdAt: new Date(),
        },
        {
          id: "2",
          component: "API Server",
          status: "NORMAL",
          message: "API server is running normally",
          createdAt: new Date(),
        },
        {
          id: "3",
          component: "File Storage",
          status: "NORMAL",
          message: "File storage is running normally",
          createdAt: new Date(),
        }
      ];

      // Mock config data
      const mockConfig = [
        {
          id: "1",
          key: "system.name",
          value: "Practice Manager",
          description: "System name",
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "2",
          key: "system.version",
          value: "1.0.0",
          description: "System version",
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ];

      // Mock database stats
      const mockDbStats = [
        {
          db_size: 1024 * 1024 * 100, // 100 MB
          active_connections: 5
        }
      ];

      return {
        components: mockComponents,
        config: mockConfig,
        dbStats: mockDbStats,
        systemTime: new Date(),
      };
    } catch (error) {
      console.error("Error in getStatus:", error);
      return {
        components: [],
        config: [],
        dbStats: [{ db_size: 0, active_connections: 0 }],
        systemTime: new Date(),
        error: "Failed to fetch system status"
      };
    }
  }),

  getStatusHistory: adminProcedure
    .input(
      z.object({
        component: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
      })
    )
    .query(async ({ ctx, input }) => {
      const logs = await ctx.prisma.systemStatusLog.findMany({
        where: input.component ? { component: input.component } : undefined,
        orderBy: { createdAt: "desc" },
        take: input.limit,
      });

      return logs;
    }),

  logStatus: adminProcedure
    .input(
      z.object({
        component: z.string(),
        status: z.nativeEnum(SystemStatus),
        message: z.string().optional(),
        details: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const log = await ctx.prisma.systemStatusLog.create({
        data: {
          component: input.component,
          status: input.status,
          message: input.message,
          details: input.details,
        },
      });

      return log;
    }),

  getConfig: adminProcedure.query(async ({ ctx }) => {
    const config = await ctx.prisma.systemConfig.findMany({
      orderBy: { key: "asc" },
    });

    return config.map((item) => ({
      ...item,
      value: item.isSecret ? "********" : item.value,
    }));
  }),

  getConfigValue: protectedProcedure
    .input(z.object({ key: z.string() }))
    .query(async ({ ctx, input }) => {
      const config = await ctx.prisma.systemConfig.findUnique({
        where: { key: input.key },
      });

      if (!config) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Configuration not found",
        });
      }

      // Only admins can see secret values
      if (config.isSecret && ctx.session.user.role !== "ADMIN") {
        return { ...config, value: "********" };
      }

      return config;
    }),

  setConfig: adminProcedure
    .input(
      z.object({
        key: z.string(),
        value: z.string(),
        description: z.string().optional(),
        isSecret: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { key, ...data } = input;

      const config = await ctx.prisma.systemConfig.upsert({
        where: { key },
        update: data,
        create: {
          key,
          value: data.value,
          description: data.description,
          isSecret: data.isSecret ?? false,
        },
      });

      return {
        ...config,
        value: config.isSecret ? "********" : config.value,
      };
    }),

  deleteConfig: adminProcedure
    .input(z.object({ key: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.systemConfig.delete({
        where: { key: input.key },
      });

      return { success: true };
    }),

  getFinancialPeriods: adminProcedure.query(async ({ ctx }) => {
    const periods = await ctx.prisma.financialPeriod.findMany({
      include: {
        closedBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { startDate: "desc" },
    });

    return periods;
  }),

  createFinancialPeriod: adminProcedure
    .input(
      z.object({
        name: z.string().min(1),
        startDate: z.date(),
        endDate: z.date(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Validate dates
      if (input.startDate >= input.endDate) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Start date must be before end date",
        });
      }

      // Check for overlapping periods
      const overlapping = await ctx.prisma.financialPeriod.findFirst({
        where: {
          OR: [
            {
              startDate: { lte: input.endDate },
              endDate: { gte: input.startDate },
            },
          ],
        },
      });

      if (overlapping) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Financial period overlaps with an existing period",
        });
      }

      const period = await ctx.prisma.financialPeriod.create({
        data: {
          name: input.name,
          startDate: input.startDate,
          endDate: input.endDate,
        },
      });

      return period;
    }),

  closeFinancialPeriod: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const period = await ctx.prisma.financialPeriod.findUnique({
        where: { id: input.id },
      });

      if (!period) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Financial period not found",
        });
      }

      if (period.isClosed) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Financial period is already closed",
        });
      }

      const updatedPeriod = await ctx.prisma.financialPeriod.update({
        where: { id: input.id },
        data: {
          isClosed: true,
          closedAt: new Date(),
          closedBy: {
            connect: { id: ctx.session.user.id },
          },
        },
        include: {
          closedBy: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return updatedPeriod;
    }),
});
