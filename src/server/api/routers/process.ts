import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "../trpc";

export const processRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const processes = await ctx.prisma.process.findMany({
      include: {
        _count: {
          select: {
            matters: true,
            stages: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return processes;
  }),

  getActive: protectedProcedure.query(async ({ ctx }) => {
    const processes = await ctx.prisma.process.findMany({
      where: { isActive: true },
      include: {
        stages: {
          where: { isActive: true },
          orderBy: { order: "asc" },
        },
      },
      orderBy: { name: "asc" },
    });

    return processes;
  }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const process = await ctx.prisma.process.findUnique({
        where: { id: input.id },
        include: {
          stages: {
            orderBy: { order: "asc" },
          },
          _count: {
            select: {
              matters: true,
            },
          },
        },
      });

      if (!process) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Process not found",
        });
      }

      return process;
    }),

  create: adminProcedure
    .input(
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        stages: z
          .array(
            z.object({
              name: z.string().min(1),
              description: z.string().optional(),
            })
          )
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const existingProcess = await ctx.prisma.process.findUnique({
        where: { name: input.name },
      });

      if (existingProcess) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Process with this name already exists",
        });
      }

      const process = await ctx.prisma.process.create({
        data: {
          name: input.name,
          description: input.description,
          stages: input.stages
            ? {
                create: input.stages.map((stage, index) => ({
                  name: stage.name,
                  description: stage.description,
                  order: index + 1,
                })),
              }
            : undefined,
        },
        include: {
          stages: {
            orderBy: { order: "asc" },
          },
        },
      });

      return process;
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const process = await ctx.prisma.process.update({
        where: { id },
        data,
        include: {
          stages: {
            orderBy: { order: "asc" },
          },
        },
      });

      return process;
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if process is used by any matters
      const mattersCount = await ctx.prisma.matter.count({
        where: { processId: input.id },
      });

      if (mattersCount > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete process that is used by matters",
        });
      }

      await ctx.prisma.processStage.deleteMany({
        where: { processId: input.id },
      });

      await ctx.prisma.process.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  createStage: adminProcedure
    .input(
      z.object({
        processId: z.string(),
        name: z.string().min(1),
        description: z.string().optional(),
        order: z.number().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { processId, name, description, order } = input;

      // Get the process
      const process = await ctx.prisma.process.findUnique({
        where: { id: processId },
        include: {
          stages: {
            orderBy: { order: "asc" },
          },
        },
      });

      if (!process) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Process not found",
        });
      }

      // Determine the order if not provided
      const stageOrder = order ?? process.stages.length + 1;

      // If order is provided and it's not at the end, we need to reorder existing stages
      if (order && order <= process.stages.length) {
        // Update orders of existing stages
        await Promise.all(
          process.stages
            .filter((stage) => stage.order >= stageOrder)
            .map((stage) =>
              ctx.prisma.processStage.update({
                where: { id: stage.id },
                data: { order: stage.order + 1 },
              })
            )
        );
      }

      // Create the new stage
      const stage = await ctx.prisma.processStage.create({
        data: {
          name,
          description,
          order: stageOrder,
          process: {
            connect: { id: processId },
          },
        },
      });

      return stage;
    }),

  updateStage: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const stage = await ctx.prisma.processStage.update({
        where: { id },
        data,
      });

      return stage;
    }),

  deleteStage: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if stage is used by any matters
      const mattersCount = await ctx.prisma.matter.count({
        where: { stageId: input.id },
      });

      if (mattersCount > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete stage that is used by matters",
        });
      }

      // Get the stage to delete
      const stage = await ctx.prisma.processStage.findUnique({
        where: { id: input.id },
      });

      if (!stage) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Stage not found",
        });
      }

      // Delete the stage
      await ctx.prisma.processStage.delete({
        where: { id: input.id },
      });

      // Reorder remaining stages
      await ctx.prisma.processStage.updateMany({
        where: {
          processId: stage.processId,
          order: { gt: stage.order },
        },
        data: {
          order: { decrement: 1 },
        },
      });

      return { success: true };
    }),

  reorderStages: adminProcedure
    .input(
      z.object({
        processId: z.string(),
        stageIds: z.array(z.string()),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { processId, stageIds } = input;

      // Verify all stages belong to the process
      const stages = await ctx.prisma.processStage.findMany({
        where: { processId },
      });

      const stageMap = new Map(stages.map((stage) => [stage.id, stage]));

      // Check if all provided stageIds belong to the process
      for (const stageId of stageIds) {
        if (!stageMap.has(stageId)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Stage with id ${stageId} does not belong to the process`,
          });
        }
      }

      // Check if all stages from the process are included
      if (stageIds.length !== stages.length) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "All stages must be included in the reordering",
        });
      }

      // Update the order of each stage
      await Promise.all(
        stageIds.map((stageId, index) =>
          ctx.prisma.processStage.update({
            where: { id: stageId },
            data: { order: index + 1 },
          })
        )
      );

      // Get the updated process with stages
      const updatedProcess = await ctx.prisma.process.findUnique({
        where: { id: processId },
        include: {
          stages: {
            orderBy: { order: "asc" },
          },
        },
      });

      return updatedProcess;
    }),
});
