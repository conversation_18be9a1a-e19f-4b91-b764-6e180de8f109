import { z } from "zod";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "../trpc";

export const courtTariffRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        court: z.string().optional(),
        category: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, court, category, isActive } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(court && { court: { contains: court, mode: "insensitive" as const } }),
        ...(category && { category: { contains: category, mode: "insensitive" as const } }),
        ...(typeof isActive === "boolean" && { isActive }),
      };

      const [tariffs, total] = await Promise.all([
        ctx.prisma.courtTariff.findMany({
          where,
          skip,
          take: limit,
          orderBy: [{ court: "asc" }, { category: "asc" }, { description: "asc" }],
        }),
        ctx.prisma.courtTariff.count({ where }),
      ]);

      return {
        tariffs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const tariff = await ctx.prisma.courtTariff.findUnique({
        where: { id: input.id },
      });

      if (!tariff) {
        throw new Error("Court tariff not found");
      }

      return tariff;
    }),

  create: adminProcedure
    .input(
      z.object({
        court: z.string().min(1),
        description: z.string().min(1),
        amount: z.number().positive(),
        currency: z.string().default("ZAR"),
        category: z.string().optional(),
        isActive: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const tariff = await ctx.prisma.courtTariff.create({
        data: input,
      });

      return tariff;
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        court: z.string().min(1).optional(),
        description: z.string().min(1).optional(),
        amount: z.number().positive().optional(),
        currency: z.string().optional(),
        category: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const tariff = await ctx.prisma.courtTariff.update({
        where: { id },
        data,
      });

      return tariff;
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.courtTariff.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  bulkImport: adminProcedure
    .input(
      z.object({
        tariffs: z.array(
          z.object({
            court: z.string().min(1),
            description: z.string().min(1),
            amount: z.number().positive(),
            currency: z.string().default("ZAR"),
            category: z.string().optional(),
            isActive: z.boolean().default(true),
          })
        ),
        replaceExisting: z.boolean().default(false),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { tariffs, replaceExisting } = input;

      if (replaceExisting) {
        // Delete existing tariffs
        await ctx.prisma.courtTariff.deleteMany({});
      }

      // Create new tariffs
      const createdTariffs = await ctx.prisma.courtTariff.createMany({
        data: tariffs,
        skipDuplicates: !replaceExisting,
      });

      return {
        success: true,
        count: createdTariffs.count,
      };
    }),

  getCourts: protectedProcedure.query(async ({ ctx }) => {
    const courts = await ctx.prisma.courtTariff.findMany({
      select: { court: true },
      distinct: ["court"],
      where: { isActive: true },
      orderBy: { court: "asc" },
    });

    return courts.map((c) => c.court);
  }),

  getCategories: protectedProcedure.query(async ({ ctx }) => {
    const categories = await ctx.prisma.courtTariff.findMany({
      select: { category: true },
      distinct: ["category"],
      where: { 
        isActive: true,
        category: { not: null },
      },
      orderBy: { category: "asc" },
    });

    return categories.map((c) => c.category).filter(Boolean);
  }),

  search: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1),
        court: z.string().optional(),
        limit: z.number().min(1).max(50).default(20),
      })
    )
    .query(async ({ ctx, input }) => {
      const { query, court, limit } = input;

      const where = {
        isActive: true,
        ...(court && { court }),
        OR: [
          { description: { contains: query, mode: "insensitive" as const } },
          { category: { contains: query, mode: "insensitive" as const } },
        ],
      };

      const tariffs = await ctx.prisma.courtTariff.findMany({
        where,
        take: limit,
        orderBy: { description: "asc" },
      });

      return tariffs;
    }),

  getStats: adminProcedure.query(async ({ ctx }) => {
    const [total, active, courts, categories] = await Promise.all([
      ctx.prisma.courtTariff.count(),
      ctx.prisma.courtTariff.count({ where: { isActive: true } }),
      ctx.prisma.courtTariff.findMany({
        select: { court: true },
        distinct: ["court"],
      }),
      ctx.prisma.courtTariff.findMany({
        select: { category: true },
        distinct: ["category"],
        where: { category: { not: null } },
      }),
    ]);

    return {
      total,
      active,
      inactive: total - active,
      courtsCount: courts.length,
      categoriesCount: categories.length,
    };
  }),
});