import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const documentRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        matterId: z.string().optional(),
        search: z.string().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, matterId, search, tags } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(matterId && { matterId }),
        ...(search && {
          OR: [
            { filename: { contains: search, mode: "insensitive" as const } },
            { originalName: { contains: search, mode: "insensitive" as const } },
            { description: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(tags && tags.length > 0 && {
          tags: { hasSome: tags },
        }),
      };

      const [documents, total] = await Promise.all([
        ctx.prisma.document.findMany({
          where,
          include: {
            matter: {
              select: { id: true, reference: true, title: true },
            },
            user: {
              select: { id: true, name: true },
            },
            versions: {
              orderBy: { version: "desc" },
              take: 1,
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        ctx.prisma.document.count({ where }),
      ]);

      return {
        documents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const document = await ctx.prisma.document.findUnique({
        where: { id: input.id },
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
          versions: {
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
            orderBy: { version: "desc" },
          },
        },
      });

      if (!document) {
        throw new Error("Document not found");
      }

      return document;
    }),

  create: protectedProcedure
    .input(
      z.object({
        matterId: z.string(),
        filename: z.string(),
        originalName: z.string(),
        mimeType: z.string(),
        size: z.number(),
        path: z.string(),
        tags: z.array(z.string()).default([]),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const document = await ctx.prisma.document.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
        },
      });

      return document;
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        tags: z.array(z.string()).optional(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const document = await ctx.prisma.document.update({
        where: { id },
        data,
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
        },
      });

      return document;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.document.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  createVersion: protectedProcedure
    .input(
      z.object({
        documentId: z.string(),
        filename: z.string(),
        path: z.string(),
        size: z.number(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Get the latest version number
      const latestVersion = await ctx.prisma.documentVersion.findFirst({
        where: { documentId: input.documentId },
        orderBy: { version: "desc" },
      });

      const newVersion = (latestVersion?.version || 0) + 1;

      const version = await ctx.prisma.documentVersion.create({
        data: {
          ...input,
          version: newVersion,
          userId: ctx.session.user.id,
        },
        include: {
          user: {
            select: { id: true, name: true },
          },
        },
      });

      return version;
    }),

  search: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1),
        matterId: z.string().optional(),
        limit: z.number().min(1).max(50).default(20),
      })
    )
    .query(async ({ ctx, input }) => {
      const { query, matterId, limit } = input;

      const where = {
        ...(matterId && { matterId }),
        OR: [
          { filename: { search: query } },
          { originalName: { search: query } },
          { description: { search: query } },
        ],
      };

      const documents = await ctx.prisma.document.findMany({
        where,
        include: {
          matter: {
            select: { id: true, reference: true, title: true },
          },
          user: {
            select: { id: true, name: true },
          },
        },
        take: limit,
        orderBy: { _relevance: { fields: ["filename", "description"], search: query } },
      });

      return documents;
    }),

  getTags: protectedProcedure.query(async ({ ctx }) => {
    const documents = await ctx.prisma.document.findMany({
      select: { tags: true },
    });

    const allTags = documents.flatMap((doc) => doc.tags);
    const uniqueTags = [...new Set(allTags)];

    return uniqueTags.sort();
  }),
});