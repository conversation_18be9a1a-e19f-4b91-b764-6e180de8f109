import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure, attorneyProcedure } from "../trpc";

export const clientRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const clients = await ctx.prisma.client.findMany({
      include: {
        group: true,
        _count: {
          select: {
            matters: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return clients;
  }),

  getActive: protectedProcedure.query(async ({ ctx }) => {
    const clients = await ctx.prisma.client.findMany({
      where: { isActive: true },
      include: {
        group: true,
      },
      orderBy: { name: "asc" },
    });

    return clients;
  }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const client = await ctx.prisma.client.findUnique({
        where: { id: input.id },
        include: {
          group: true,
          matters: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!client) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Client not found",
        });
      }

      return client;
    }),

  create: attorneyProcedure
    .input(
      z.object({
        name: z.string().min(1),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        address: z.string().optional(),
        groupId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const client = await ctx.prisma.client.create({
        data: {
          name: input.name,
          email: input.email,
          phone: input.phone,
          address: input.address,
          group: input.groupId
            ? {
                connect: { id: input.groupId },
              }
            : undefined,
        },
        include: {
          group: true,
        },
      });

      return client;
    }),

  update: attorneyProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        address: z.string().optional(),
        groupId: z.string().optional().nullable(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, groupId, ...data } = input;

      const client = await ctx.prisma.client.update({
        where: { id },
        data: {
          ...data,
          group: groupId === null
            ? { disconnect: true }
            : groupId
            ? { connect: { id: groupId } }
            : undefined,
        },
        include: {
          group: true,
        },
      });

      return client;
    }),

  delete: attorneyProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if client has matters
      const mattersCount = await ctx.prisma.matter.count({
        where: { clientId: input.id },
      });

      if (mattersCount > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete client that has matters",
        });
      }

      await ctx.prisma.client.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  getGroups: protectedProcedure.query(async ({ ctx }) => {
    const groups = await ctx.prisma.clientGroup.findMany({
      include: {
        _count: {
          select: {
            clients: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return groups;
  }),

  getGroupById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const group = await ctx.prisma.clientGroup.findUnique({
        where: { id: input.id },
        include: {
          clients: {
            where: { isActive: true },
            orderBy: { name: "asc" },
          },
        },
      });

      if (!group) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Client group not found",
        });
      }

      return group;
    }),

  createGroup: attorneyProcedure
    .input(
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const existingGroup = await ctx.prisma.clientGroup.findUnique({
        where: { name: input.name },
      });

      if (existingGroup) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Client group with this name already exists",
        });
      }

      const group = await ctx.prisma.clientGroup.create({
        data: {
          name: input.name,
          description: input.description,
        },
      });

      return group;
    }),

  updateGroup: attorneyProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // If updating name, check for uniqueness
      if (data.name) {
        const existingGroup = await ctx.prisma.clientGroup.findFirst({
          where: {
            name: data.name,
            id: { not: id },
          },
        });

        if (existingGroup) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Client group with this name already exists",
          });
        }
      }

      const group = await ctx.prisma.clientGroup.update({
        where: { id },
        data,
      });

      return group;
    }),

  deleteGroup: attorneyProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if group has clients
      const clientsCount = await ctx.prisma.client.count({
        where: { groupId: input.id },
      });

      if (clientsCount > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete group that has clients",
        });
      }

      await ctx.prisma.clientGroup.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),
});
