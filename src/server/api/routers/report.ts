import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const reportRouter = createTRPCRouter({
  getFinancialSummary: protectedProcedure
    .input(
      z.object({
        startDate: z.date(),
        endDate: z.date(),
        matterId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { startDate, endDate, matterId } = input;

      const dateFilter = {
        gte: startDate,
        lte: endDate,
      };

      const matterFilter = matterId ? { matterId } : {};

      const [timeEntries, invoices, payments, trustTransfers] = await Promise.all([
        ctx.prisma.timeEntry.aggregate({
          where: {
            ...matterFilter,
            date: dateFilter,
            isBillable: true,
          },
          _sum: { amount: true, hours: true },
          _count: true,
        }),
        ctx.prisma.invoice.aggregate({
          where: {
            ...matterFilter,
            issueDate: dateFilter,
          },
          _sum: { total: true },
          _count: true,
        }),
        ctx.prisma.payment.aggregate({
          where: {
            date: dateFilter,
            ...(matterId && {
              invoice: { matterId },
            }),
          },
          _sum: { amount: true },
          _count: true,
        }),
        ctx.prisma.trustTransfer.aggregate({
          where: {
            date: dateFilter,
            ...(matterId && {
              trustAccount: { matterId },
            }),
          },
          _sum: { amount: true },
          _count: true,
        }),
      ]);

      return {
        period: { startDate, endDate },
        billableHours: timeEntries._sum.hours || 0,
        billableAmount: timeEntries._sum.amount || 0,
        timeEntriesCount: timeEntries._count,
        invoicesTotal: invoices._sum.total || 0,
        invoicesCount: invoices._count,
        paymentsTotal: payments._sum.amount || 0,
        paymentsCount: payments._count,
        trustTransfersTotal: trustTransfers._sum.amount || 0,
        trustTransfersCount: trustTransfers._count,
      };
    }),

  getProductivityReport: protectedProcedure
    .input(
      z.object({
        startDate: z.date(),
        endDate: z.date(),
        userId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { startDate, endDate, userId } = input;

      const where = {
        date: {
          gte: startDate,
          lte: endDate,
        },
        ...(userId && { userId }),
      };

      const timeEntries = await ctx.prisma.timeEntry.findMany({
        where,
        include: {
          user: {
            select: { id: true, name: true },
          },
          matter: {
            select: { id: true, reference: true, title: true },
          },
        },
      });

      // Group by user
      const userStats = timeEntries.reduce((acc, entry) => {
        const userId = entry.user.id;
        if (!acc[userId]) {
          acc[userId] = {
            user: entry.user,
            totalHours: 0,
            billableHours: 0,
            totalAmount: 0,
            entriesCount: 0,
            matters: new Set(),
          };
        }

        acc[userId].totalHours += Number(entry.hours);
        acc[userId].totalAmount += Number(entry.amount);
        acc[userId].entriesCount += 1;
        acc[userId].matters.add(entry.matter.id);

        if (entry.isBillable) {
          acc[userId].billableHours += Number(entry.hours);
        }

        return acc;
      }, {} as Record<string, any>);

      // Convert to array and add calculated fields
      const productivity = Object.values(userStats).map((stats: any) => ({
        ...stats,
        mattersCount: stats.matters.size,
        averageRate: stats.totalHours > 0 ? stats.totalAmount / stats.totalHours : 0,
        utilizationRate: stats.totalHours > 0 ? stats.billableHours / stats.totalHours : 0,
        matters: undefined, // Remove Set from response
      }));

      return productivity;
    }),

  getWIPReport: protectedProcedure.query(async ({ ctx }) => {
    const matters = await ctx.prisma.matter.findMany({
      where: { status: "ACTIVE" },
      include: {
        timeEntries: {
          where: { isInvoiced: false, isBillable: true },
        },
        user: {
          select: { id: true, name: true },
        },
      },
    });

    const wipData = matters.map((matter) => {
      const unbilledAmount = matter.timeEntries.reduce(
        (sum, entry) => sum + Number(entry.amount),
        0
      );
      const unbilledHours = matter.timeEntries.reduce(
        (sum, entry) => sum + Number(entry.hours),
        0
      );

      return {
        matter: {
          id: matter.id,
          reference: matter.reference,
          title: matter.title,
          clientName: matter.clientName,
        },
        attorney: matter.user,
        unbilledHours,
        unbilledAmount,
        lastActivity: matter.timeEntries.length > 0 
          ? Math.max(...matter.timeEntries.map(e => e.date.getTime()))
          : matter.createdAt.getTime(),
      };
    });

    return wipData.sort((a, b) => b.unbilledAmount - a.unbilledAmount);
  }),

  getAgedDebtorsReport: protectedProcedure.query(async ({ ctx }) => {
    const invoices = await ctx.prisma.invoice.findMany({
      where: {
        status: { in: ["SENT", "OVERDUE"] },
      },
      include: {
        matter: {
          select: { id: true, reference: true, title: true, clientName: true },
        },
        payments: true,
      },
    });

    const today = new Date();
    const agedDebtors = invoices.map((invoice) => {
      const totalPaid = invoice.payments.reduce(
        (sum, payment) => sum + Number(payment.amount),
        0
      );
      const outstanding = Number(invoice.total) - totalPaid;
      const daysPastDue = Math.floor(
        (today.getTime() - invoice.dueDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      let ageCategory = "Current";
      if (daysPastDue > 0) {
        if (daysPastDue <= 30) ageCategory = "1-30 days";
        else if (daysPastDue <= 60) ageCategory = "31-60 days";
        else if (daysPastDue <= 90) ageCategory = "61-90 days";
        else ageCategory = "90+ days";
      }

      return {
        invoice: {
          id: invoice.id,
          number: invoice.number,
          issueDate: invoice.issueDate,
          dueDate: invoice.dueDate,
          total: invoice.total,
        },
        matter: invoice.matter,
        outstanding,
        daysPastDue: Math.max(0, daysPastDue),
        ageCategory,
      };
    });

    return agedDebtors
      .filter((debtor) => debtor.outstanding > 0)
      .sort((a, b) => b.daysPastDue - a.daysPastDue);
  }),

  getTrustAccountReport: protectedProcedure.query(async ({ ctx }) => {
    const accounts = await ctx.prisma.trustAccount.findMany({
      include: {
        matter: {
          select: { id: true, reference: true, title: true, clientName: true },
        },
        transfers: {
          orderBy: { date: "desc" },
          take: 5,
        },
      },
    });

    const totalBalance = accounts.reduce(
      (sum, account) => sum + Number(account.balance),
      0
    );

    return {
      accounts: accounts.map((account) => ({
        id: account.id,
        name: account.name,
        balance: account.balance,
        matter: account.matter,
        lastActivity: account.transfers[0]?.date || account.createdAt,
      })),
      totalBalance,
      accountsCount: accounts.length,
    };
  }),

  exportLEDES: protectedProcedure
    .input(
      z.object({
        invoiceId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.invoiceId },
        include: {
          matter: true,
          timeEntries: {
            include: {
              user: true,
            },
          },
          lineItems: true,
        },
      });

      if (!invoice) {
        throw new Error("Invoice not found");
      }

      // Generate LEDES format data
      const ledesData = invoice.timeEntries.map((entry, index) => ({
        INVOICE_NUMBER: invoice.number,
        INVOICE_DATE: invoice.issueDate.toISOString().split('T')[0],
        INVOICE_TOTAL: invoice.total,
        CLIENT_ID: invoice.matter.reference,
        MATTER_ID: invoice.matter.id,
        LINE_ITEM_NUMBER: index + 1,
        EXP_FEE_INV_ADJ_TYPE: "F", // Fee
        LINE_ITEM_DATE: entry.date.toISOString().split('T')[0],
        LINE_ITEM_NUMBER_OF_UNITS: entry.hours,
        LINE_ITEM_ADJUSTMENT_AMOUNT: 0,
        LINE_ITEM_TOTAL: entry.amount,
        LINE_ITEM_DESCRIPTION: entry.description,
        LINE_ITEM_UNIT_COST: entry.rate,
        TIMEKEEPER_ID: entry.user.id,
        TIMEKEEPER_NAME: entry.user.name,
        TIMEKEEPER_CLASSIFICATION: "Attorney",
      }));

      return ledesData;
    }),
});