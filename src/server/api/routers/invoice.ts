import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { InvoiceStatus, PaymentMethod } from "@prisma/client";

export const invoiceRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        status: z.nativeEnum(InvoiceStatus).optional(),
        matterId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, status, matterId } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(status && { status }),
        ...(matterId && { matterId }),
      };

      const [invoices, total] = await Promise.all([
        ctx.prisma.invoice.findMany({
          where,
          include: {
            matter: {
              select: { id: true, reference: true, title: true, clientName: true },
            },
            user: {
              select: { id: true, name: true },
            },
            timeEntries: {
              select: { id: true, description: true, hours: true, rate: true, amount: true },
            },
            lineItems: true,
            payments: {
              select: { id: true, amount: true, date: true, method: true },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        ctx.prisma.invoice.count({ where }),
      ]);

      return {
        invoices,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.id },
        include: {
          matter: {
            select: { 
              id: true, 
              reference: true, 
              title: true, 
              clientName: true,
              clientEmail: true,
              clientPhone: true,
            },
          },
          user: {
            select: { id: true, name: true, email: true },
          },
          timeEntries: {
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
          },
          lineItems: true,
          payments: {
            include: {
              user: {
                select: { id: true, name: true },
              },
            },
            orderBy: { date: "desc" },
          },
        },
      });

      if (!invoice) {
        throw new Error("Invoice not found");
      }

      return invoice;
    }),

  create: protectedProcedure
    .input(
      z.object({
        matterId: z.string(),
        number: z.string(),
        dueDate: z.date(),
        timeEntryIds: z.array(z.string()).default([]),
        lineItems: z.array(
          z.object({
            description: z.string(),
            quantity: z.number().default(1),
            rate: z.number(),
            amount: z.number(),
          })
        ).default([]),
        notes: z.string().optional(),
        vatRate: z.number().default(0.15),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { timeEntryIds, lineItems, vatRate, ...invoiceData } = input;

      // Calculate totals from time entries
      let timeEntriesTotal = 0;
      if (timeEntryIds.length > 0) {
        const timeEntries = await ctx.prisma.timeEntry.findMany({
          where: { id: { in: timeEntryIds } },
        });
        timeEntriesTotal = timeEntries.reduce((sum, entry) => sum + Number(entry.amount), 0);
      }

      // Calculate totals from line items
      const lineItemsTotal = lineItems.reduce((sum, item) => sum + item.amount, 0);

      const subtotal = timeEntriesTotal + lineItemsTotal;
      const vatAmount = subtotal * vatRate;
      const total = subtotal + vatAmount;

      const invoice = await ctx.prisma.invoice.create({
        data: {
          ...invoiceData,
          userId: ctx.session.user.id,
          subtotal,
          vatAmount,
          total,
          lineItems: {
            create: lineItems,
          },
        },
        include: {
          matter: {
            select: { id: true, reference: true, title: true, clientName: true },
          },
          user: {
            select: { id: true, name: true },
          },
          lineItems: true,
        },
      });

      // Link time entries to invoice
      if (timeEntryIds.length > 0) {
        await ctx.prisma.timeEntry.updateMany({
          where: { id: { in: timeEntryIds } },
          data: { invoiceId: invoice.id, isInvoiced: true },
        });
      }

      return invoice;
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        status: z.nativeEnum(InvoiceStatus).optional(),
        dueDate: z.date().optional(),
        notes: z.string().optional(),
        paidAt: z.date().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const invoice = await ctx.prisma.invoice.update({
        where: { id },
        data,
        include: {
          matter: {
            select: { id: true, reference: true, title: true, clientName: true },
          },
          user: {
            select: { id: true, name: true },
          },
          lineItems: true,
        },
      });

      return invoice;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Unlink time entries
      await ctx.prisma.timeEntry.updateMany({
        where: { invoiceId: input.id },
        data: { invoiceId: null, isInvoiced: false },
      });

      await ctx.prisma.invoice.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  addPayment: protectedProcedure
    .input(
      z.object({
        invoiceId: z.string(),
        amount: z.number().positive(),
        method: z.nativeEnum(PaymentMethod),
        reference: z.string().optional(),
        description: z.string().optional(),
        date: z.date().default(() => new Date()),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const payment = await ctx.prisma.payment.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
      });

      // Check if invoice is fully paid
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.invoiceId },
        include: { payments: true },
      });

      if (invoice) {
        const totalPaid = invoice.payments.reduce(
          (sum, payment) => sum + Number(payment.amount),
          0
        );

        if (totalPaid >= Number(invoice.total)) {
          await ctx.prisma.invoice.update({
            where: { id: input.invoiceId },
            data: { 
              status: "PAID",
              paidAt: new Date(),
            },
          });
        }
      }

      return payment;
    }),

  getStats: protectedProcedure.query(async ({ ctx }) => {
    const [total, draft, sent, paid, overdue, totalAmount, paidAmount] = await Promise.all([
      ctx.prisma.invoice.count(),
      ctx.prisma.invoice.count({ where: { status: "DRAFT" } }),
      ctx.prisma.invoice.count({ where: { status: "SENT" } }),
      ctx.prisma.invoice.count({ where: { status: "PAID" } }),
      ctx.prisma.invoice.count({ where: { status: "OVERDUE" } }),
      ctx.prisma.invoice.aggregate({
        _sum: { total: true },
      }),
      ctx.prisma.invoice.aggregate({
        where: { status: "PAID" },
        _sum: { total: true },
      }),
    ]);

    return {
      total,
      draft,
      sent,
      paid,
      overdue,
      totalAmount: totalAmount._sum.total || 0,
      paidAmount: paidAmount._sum.total || 0,
      outstandingAmount: (totalAmount._sum.total || 0) - (paidAmount._sum.total || 0),
    };
  }),
});