# GhostPractice Clone - Practice Management Platform

A comprehensive, production-ready practice management platform built with Next.js 15, featuring all the core functionality of GhostPractice for legal professionals.

## 🚀 Features

### Core Modules
- **User & Role Management** - <PERSON><PERSON>, Attorney, Secretary, Bookkeeper roles with MFA support
- **Matter Management** - Complete case lifecycle with parties, documents, and custom fields
- **Time & Billing** - Billable timers, invoice generation, LEDES export
- **Trust & Business Accounting** - Client ledgers, trust accounts, reconciliation
- **Document Management** - Upload, versioning, full-text search, PDF/DOCX preview
- **Workflow & Tasks** - Matter-linked tasks, Kanban boards, recurring templates
- **Court Tariff Tables** - Admin management, bulk import, auto-application
- **Reporting & Dashboards** - Financial reports, productivity metrics, aged debtors
- **Notifications** - Email, in-app, SMS integration hooks

### Technical Features
- **Multi-tenant Architecture** - Schema-per-tenant with PostgreSQL
- **Type-safe APIs** - tRPC for end-to-end type safety
- **Modern UI** - shadcn/ui components with Tailwind CSS
- **Authentication** - NextAuth with email/password + Azure AD OIDC
- **Real-time Updates** - WebSocket integration for live notifications
- **Full-text Search** - PostgreSQL pg_trgm extension
- **File Management** - Secure upload/download with versioning
- **PDF Generation** - Invoice and report generation
- **Data Validation** - Zod schemas throughout
- **Testing** - Unit tests (Vitest) and E2E tests (Playwright)

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router, TypeScript, React Server Components)
- **Database**: PostgreSQL 16 with Prisma ORM
- **API**: tRPC for type-safe client-server communication
- **Authentication**: NextAuth.js with multiple providers
- **UI**: shadcn/ui + Tailwind CSS
- **State Management**: Zustand for client state
- **Validation**: Zod schemas
- **Testing**: Vitest + Testing Library + Playwright
- **DevOps**: Docker + docker-compose + GitHub Actions

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ghostpractice-clone
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start with Docker (Recommended)**
   ```bash
   docker-compose up -d
   ```
   This starts:
   - Application server (localhost:3000)
   - PostgreSQL database (localhost:5432)
   - MailHog for email testing (localhost:8025)

5. **Or run locally**
   ```bash
   # Start database
   docker-compose up -d db mailhog
   
   # Generate Prisma client
   npm run db:generate
   
   # Run migrations
   npm run db:migrate
   
   # Seed database
   npm run db:seed
   
   # Start development server
   npm run dev
   ```

6. **Access the application**
   - Application: http://localhost:3000
   - Email testing: http://localhost:8025
   - Database admin: `npm run db:studio`

### Demo Accounts

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Attorney | <EMAIL> | admin123 |
| Secretary | <EMAIL> | admin123 |
| Bookkeeper | <EMAIL> | admin123 |

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main application pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   └── ...               # Feature components
├── lib/                  # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── db.ts             # Prisma client
│   ├── utils.ts          # Helper functions
│   └── trpc/             # tRPC configuration
├── server/               # Server-side code
│   └── api/              # tRPC routers
├── types/                # TypeScript type definitions
└── ...
```

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# E2E tests with UI
npm run test:e2e:ui
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Production
```bash
docker build -t ghostpractice-clone .
docker run -p 3000:3000 ghostpractice-clone
```

### Environment Variables

Key environment variables for production:

```env
# Database
DATABASE_URL="********************************/dbname"

# NextAuth
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://yourdomain.com"

# Azure AD (Optional)
AZURE_AD_CLIENT_ID="your-client-id"
AZURE_AD_CLIENT_SECRET="your-client-secret"
AZURE_AD_TENANT_ID="your-tenant-id"

# Email
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-user"
SMTP_PASSWORD="your-smtp-password"
```

## 📊 Key Features Walkthrough

### Matter Management
- Create and manage legal matters with full lifecycle tracking
- Add parties (clients, opposing parties, witnesses)
- Custom fields for matter-specific data
- Stage tracking and status management

### Time & Billing
- Built-in timer for billable hours
- Flexible rate structures per user
- Invoice generation with PDF export
- LEDES billing format support
- Payment tracking and reconciliation

### Document Management
- Secure file upload with versioning
- Full-text search across documents
- In-browser PDF and DOCX preview
- Tagging and categorization
- Access control and audit trails

### Trust Accounting
- Separate trust accounts per matter
- Deposit, withdrawal, and transfer tracking
- Interest allocation and compliance reporting
- Reconciliation tools
- South African trust compliance features

### Workflow Management
- Task creation and assignment
- Kanban board visualization
- Recurring task templates
- Due date notifications
- Matter-linked task organization

## 🔧 Development

### Database Operations
```bash
# Generate Prisma client
npm run db:generate

# Create migration
npm run db:migrate

# Push schema changes
npm run db:push

# Seed database
npm run db:seed

# Open Prisma Studio
npm run db:studio
```

### Code Quality
```bash
# Lint code
npm run lint

# Type check
npm run type-check

# Format code (if Prettier is configured)
npm run format
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in the `/docs` folder
- Review the demo data and examples

## 🔄 Updates

This platform is actively maintained and updated. Key areas of ongoing development:
- Enhanced reporting capabilities
- Mobile responsiveness improvements
- Additional integrations (accounting software, court systems)
- Advanced workflow automation
- Multi-language support

---

Built with ❤️ for legal professionals who need powerful, reliable practice management tools.