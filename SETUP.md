# GhostPractice Clone - Setup Guide

This guide will help you set up and run the GhostPractice Clone practice management platform locally.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Docker & Docker Compose** - [Download here](https://www.docker.com/products/docker-desktop/)
- **Git** - [Download here](https://git-scm.com/)

## Quick Start (Recommended)

The fastest way to get started is using Docker Compose:

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd ghostpractice-clone

# Copy environment variables
cp .env.example .env

# Edit .env file with your preferences (optional for local development)
nano .env
```

### 2. Start with Docker

```bash
# Start all services (app, database, email server)
docker-compose up -d

# Wait for services to start (about 30-60 seconds)
# Check logs if needed
docker-compose logs -f app
```

### 3. Initialize Database

```bash
# Generate Prisma client
docker-compose exec app npm run db:generate

# Run database migrations
docker-compose exec app npm run db:migrate

# Seed with demo data
docker-compose exec app npm run db:seed
```

### 4. Access the Application

- **Application**: http://localhost:3000
- **Email Testing (MailHog)**: http://localhost:8025
- **Database Admin**: Run `docker-compose exec app npm run db:studio`

## Manual Setup (Alternative)

If you prefer to run without Docker:

### 1. Install Dependencies

```bash
npm install
```

### 2. Setup Database

```bash
# Start PostgreSQL (using Docker)
docker run --name ghostpractice-db -e POSTGRES_PASSWORD=password -e POSTGRES_DB=ghostpractice -p 5432:5432 -d postgres:16-alpine

# Or install PostgreSQL locally and create database
createdb ghostpractice
```

### 3. Configure Environment

```bash
cp .env.example .env
```

Edit `.env` file:
```env
DATABASE_URL="postgresql://postgres:password@localhost:5432/ghostpractice?schema=public"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Setup Database Schema

```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed with demo data
npm run db:seed
```

### 5. Start Development Server

```bash
npm run dev
```

## Demo Accounts

After seeding, you can login with these accounts:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | admin123 | Full system access |
| Attorney | <EMAIL> | admin123 | Legal professional |
| Secretary | <EMAIL> | admin123 | Administrative support |
| Bookkeeper | <EMAIL> | admin123 | Financial management |

## Features Overview

### Core Modules Available

✅ **User & Role Management**
- Multi-role system (Admin, Attorney, Secretary, Bookkeeper)
- Secure authentication with NextAuth
- User profile management

✅ **Matter Management**
- Create and manage legal matters
- Client information and parties
- Custom fields and stage tracking
- Matter archiving and status management

✅ **Time & Billing**
- Time entry tracking
- Billable rate management
- Invoice generation
- Payment tracking

✅ **Trust Accounting**
- Trust account management
- Deposit and withdrawal tracking
- Balance reconciliation
- Compliance reporting

✅ **Document Management**
- File upload and storage
- Document versioning
- Tagging and categorization
- Full-text search capabilities

✅ **Task & Workflow Management**
- Task creation and assignment
- Due date tracking
- Kanban board view
- Recurring task templates

✅ **Court Tariff Management**
- Tariff table management
- Bulk import functionality
- Auto-application to invoices

✅ **Reporting & Analytics**
- Financial summaries
- Productivity reports
- Work-in-progress tracking
- Aged debtors analysis

✅ **Notification System**
- In-app notifications
- Email integration ready
- Task reminders

## Development Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema changes
npm run db:migrate      # Run migrations
npm run db:seed         # Seed demo data
npm run db:studio       # Open database admin

# Testing
npm run test            # Run unit tests
npm run test:e2e        # Run E2E tests
npm run test:e2e:ui     # Run E2E tests with UI

# Code Quality
npm run lint            # Lint code
npm run type-check      # TypeScript check
```

## Project Structure

```
├── prisma/                 # Database schema and migrations
│   ├── schema.prisma      # Database schema
│   └── seed.ts            # Demo data seeder
├── src/
│   ├── app/               # Next.js App Router pages
│   │   ├── api/           # API routes (NextAuth, tRPC)
│   │   ├── auth/          # Authentication pages
│   │   ├── dashboard/     # Main application
│   │   └── globals.css    # Global styles
│   ├── components/        # React components
│   │   └── ui/            # shadcn/ui components
│   ├── lib/               # Utility libraries
│   │   ├── auth.ts        # NextAuth configuration
│   │   ├── db.ts          # Prisma client
│   │   └── utils.ts       # Helper functions
│   ├── server/            # Server-side code
│   │   └── api/           # tRPC routers
│   └── types/             # TypeScript definitions
├── tests/                 # Test files
└── docker-compose.yml     # Docker services
```

## Troubleshooting

### Common Issues

**1. Database Connection Error**
```bash
# Check if PostgreSQL is running
docker-compose ps

# Restart database service
docker-compose restart db
```

**2. Prisma Client Not Generated**
```bash
# Regenerate Prisma client
npm run db:generate
```

**3. Port Already in Use**
```bash
# Check what's using port 3000
lsof -i :3000

# Kill the process or change port in docker-compose.yml
```

**4. TypeScript Errors**
```bash
# Install dependencies
npm install

# Check TypeScript configuration
npm run type-check
```

### Reset Everything

If you need to start fresh:

```bash
# Stop all services
docker-compose down -v

# Remove all data
docker-compose down -v --remove-orphans

# Start fresh
docker-compose up -d
```

## Production Deployment

### Environment Variables

Set these for production:

```env
NODE_ENV=production
DATABASE_URL="your-production-database-url"
NEXTAUTH_SECRET="secure-random-secret"
NEXTAUTH_URL="https://yourdomain.com"

# Email (for notifications)
SMTP_HOST="your-smtp-server"
SMTP_PORT="587"
SMTP_USER="your-email"
SMTP_PASSWORD="your-password"

# Azure AD (optional)
AZURE_AD_CLIENT_ID="your-client-id"
AZURE_AD_CLIENT_SECRET="your-client-secret"
AZURE_AD_TENANT_ID="your-tenant-id"
```

### Build and Deploy

```bash
# Build the application
npm run build

# Start production server
npm start
```

## Support

For issues and questions:

1. Check this setup guide
2. Review the main README.md
3. Check the demo data and examples
4. Create an issue in the repository

## Next Steps

Once you have the application running:

1. **Explore the Dashboard** - Login and familiarize yourself with the interface
2. **Create a Matter** - Try creating a new legal matter
3. **Add Time Entries** - Track some billable time
4. **Generate an Invoice** - Create and preview an invoice
5. **Upload Documents** - Test the document management system
6. **Review Reports** - Check out the reporting capabilities

The application includes comprehensive demo data to help you understand all features.